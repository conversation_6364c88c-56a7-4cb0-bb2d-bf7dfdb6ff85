#!/usr/bin/env python3
"""
Test script to verify the text generation fix for truncation issues.
"""

import requests
import json
import time

def test_generation(base_url="http://localhost:8000", model_name="qwen3-8b"):
    """Test text generation with different max_tokens values."""
    
    # Test prompt - the same Chinese text about sun and moon
    test_prompt = "太阳和月亮哪个更"大"？这个问题可能有两种理解方式："
    
    # Test with different max_tokens values
    test_cases = [
        {"max_tokens": 100, "description": "Original default (100 tokens)"},
        {"max_tokens": 500, "description": "Medium length (500 tokens)"},
        {"max_tokens": 2048, "description": "New default (2048 tokens)"},
    ]
    
    print("Testing text generation with different max_tokens values...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['description']}")
        print("-" * 40)
        
        # Prepare request
        request_data = {
            "model_name": model_name,
            "prompt": test_prompt,
            "max_tokens": test_case["max_tokens"],
            "temperature": 0.7,
            "top_p": 0.9
        }
        
        try:
            # Send request
            start_time = time.time()
            response = requests.post(
                f"{base_url}/generate",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success!")
                print(f"Generated text: {result['generated_text']}")
                print(f"Tokens generated: {result['tokens_generated']}")
                print(f"Latency: {result['latency']:.2f}s")
                print(f"Engine: {result['engine']}")
                
                # Check if generation was truncated unexpectedly
                if result['tokens_generated'] < 10:
                    print("⚠️  WARNING: Very few tokens generated - possible early stopping")
                elif result['tokens_generated'] >= test_case["max_tokens"] * 0.9:
                    print("ℹ️  Generation likely hit max_tokens limit")
                else:
                    print("✅ Generation completed normally")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")

def check_server_status(base_url="http://localhost:8000"):
    """Check if the server is running and models are loaded."""
    try:
        # Check health
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Server is healthy")
            print(f"Models loaded: {health_data['models_loaded']}")
            print(f"Available engines: {health_data['engines']}")
            
            # List models
            models_response = requests.get(f"{base_url}/models", timeout=5)
            if models_response.status_code == 200:
                models = models_response.json()
                print(f"Loaded models: {models}")
                return len(models) > 0
            else:
                print("❌ Could not retrieve model list")
                return False
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Could not connect to server: {e}")
        return False

if __name__ == "__main__":
    print("Text Generation Fix Test")
    print("=" * 60)
    
    # Check server status first
    if check_server_status():
        print("\nServer is ready. Starting generation tests...\n")
        test_generation()
    else:
        print("\n❌ Server is not ready. Please start the server first:")
        print("   python app.py")
        print("   # or")
        print("   python app_vllm_real.py")
        print("   # or")
        print("   python app_multi_engine.py")
