#!/usr/bin/env python3
"""
CUDA 12.x 升级方案和风险评估
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def check_current_environment():
    """检查当前环境"""
    print("🔍 检查当前环境...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA 版本: {torch.version.cuda}")
        print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU 数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"   GPU {i}: {props.name}")
    except ImportError:
        print("❌ PyTorch 未安装")
    
    # 检查其他相关包
    packages_to_check = [
        'unsloth', 'transformers', 'accelerate', 'bitsandbytes',
        'flash-attn', 'xformers', 'triton'
    ]
    
    print("\n📦 相关包状态:")
    for pkg in packages_to_check:
        try:
            result = subprocess.run([sys.executable, '-c', f'import {pkg}; print({pkg}.__version__)'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ {pkg}: {version}")
            else:
                print(f"❌ {pkg}: 导入失败")
        except:
            print(f"❌ {pkg}: 未安装")

def create_backup_plan():
    """创建备份方案"""
    print("\n💾 创建备份方案...")
    
    backup_script = '''#!/bin/bash
# CUDA 升级前的环境备份脚本

echo "🔄 创建环境备份..."

# 1. 备份 pip 包列表
pip freeze > requirements_backup_$(date +%Y%m%d_%H%M%S).txt
echo "✅ 已备份 pip 包列表"

# 2. 备份虚拟环境（如果空间足够）
if [ -d "venv" ]; then
    echo "📁 检测到虚拟环境，创建备份..."
    cp -r venv venv_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "⚠️ 虚拟环境太大，跳过完整备份"
fi

# 3. 测试当前环境
python -c "
import torch
print(f'备份前环境测试:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
" > environment_backup_$(date +%Y%m%d_%H%M%S).log

echo "✅ 备份完成"
'''
    
    with open('backup_environment.sh', 'w') as f:
        f.write(backup_script)
    
    os.chmod('backup_environment.sh', 0o755)
    print("✅ 备份脚本已创建: backup_environment.sh")

def create_upgrade_script():
    """创建升级脚本"""
    print("\n🚀 创建升级脚本...")
    
    upgrade_script = '''#!/bin/bash
# CUDA 12.x 升级脚本

set -e  # 遇到错误立即退出

echo "🚀 开始 CUDA 12.x 升级..."

# 1. 卸载当前 PyTorch 相关包
echo "🗑️ 卸载当前 PyTorch 包..."
pip uninstall -y torch torchvision torchaudio

# 2. 卸载 CUDA 11.x 相关包
echo "🗑️ 卸载 CUDA 11.x 包..."
pip uninstall -y nvidia-cuda-cupti-cu11 nvidia-cuda-nvrtc-cu11 nvidia-cuda-runtime-cu11 || true

# 3. 清理缓存
echo "🧹 清理缓存..."
pip cache purge
python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true

# 4. 安装 PyTorch CUDA 12.x 版本
echo "📦 安装 PyTorch CUDA 12.x..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

# 5. 验证安装
echo "✅ 验证安装..."
python -c "
import torch
print(f'新环境测试:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU 数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f'GPU {i}: {props.name}')
"

echo "🎉 CUDA 12.x 升级完成！"
'''
    
    with open('upgrade_cuda.sh', 'w') as f:
        f.write(upgrade_script)
    
    os.chmod('upgrade_cuda.sh', 0o755)
    print("✅ 升级脚本已创建: upgrade_cuda.sh")

def create_rollback_script():
    """创建回滚脚本"""
    print("\n🔄 创建回滚脚本...")
    
    rollback_script = '''#!/bin/bash
# CUDA 回滚脚本

set -e

echo "🔄 开始回滚到 CUDA 11.8..."

# 1. 卸载 CUDA 12.x 版本
echo "🗑️ 卸载 CUDA 12.x 包..."
pip uninstall -y torch torchvision torchaudio

# 2. 重新安装 CUDA 11.8 版本
echo "📦 重新安装 CUDA 11.8 版本..."
pip install torch==2.7.1+cu118 torchvision==0.22.1+cu118 torchaudio==2.7.1+cu118 --index-url https://download.pytorch.org/whl/cu118

# 3. 验证回滚
echo "✅ 验证回滚..."
python -c "
import torch
print(f'回滚后环境:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
"

echo "✅ 回滚完成！"
'''
    
    with open('rollback_cuda.sh', 'w') as f:
        f.write(rollback_script)
    
    os.chmod('rollback_cuda.sh', 0o755)
    print("✅ 回滚脚本已创建: rollback_cuda.sh")

def create_vllm_test_script():
    """创建 vLLM 测试脚本"""
    print("\n🧪 创建 vLLM 测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
"""
CUDA 12.x 升级后的 vLLM 测试
"""

def test_vllm_installation():
    """测试 vLLM 安装"""
    print("📦 测试 vLLM 安装...")
    
    try:
        # 尝试安装 vLLM
        import subprocess
        import sys
        
        print("正在安装 vLLM...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "vllm"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ vLLM 安装成功")
        else:
            print(f"❌ vLLM 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ vLLM 安装异常: {e}")
        return False
    
    # 测试导入
    try:
        from vllm import LLM, SamplingParams
        print("✅ vLLM 导入成功")
        return True
    except Exception as e:
        print(f"❌ vLLM 导入失败: {e}")
        return False

def test_vllm_with_model():
    """测试 vLLM 加载模型"""
    print("🚀 测试 vLLM 模型加载...")
    
    try:
        from vllm import LLM, SamplingParams
        
        # 使用较小的配置测试
        llm = LLM(
            model="./models/qwen3-8b",
            tensor_parallel_size=2,
            gpu_memory_utilization=0.85,
            max_model_len=4096,
            trust_remote_code=True,
            dtype="bfloat16"
        )
        
        print("✅ vLLM 模型加载成功")
        
        # 简单测试生成
        sampling_params = SamplingParams(temperature=0.7, top_p=0.9, max_tokens=100)
        outputs = llm.generate(["你好，请介绍一下自己。"], sampling_params)
        
        for output in outputs:
            generated_text = output.outputs[0].text
            print(f"✅ 生成测试成功: {generated_text[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ vLLM 模型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 vLLM CUDA 12.x 兼容性测试")
    print("=" * 50)
    
    if test_vllm_installation():
        test_vllm_with_model()
    
    print("=" * 50)
    print("测试完成！")
'''
    
    with open('test_vllm_cuda12.py', 'w') as f:
        f.write(test_script)
    
    print("✅ vLLM 测试脚本已创建: test_vllm_cuda12.py")

def show_upgrade_plan():
    """显示完整升级方案"""
    print("\n" + "="*60)
    print("🎯 CUDA 12.x 升级完整方案")
    print("="*60)
    
    print("""
📋 升级步骤:

1️⃣ 环境备份
   ./backup_environment.sh

2️⃣ 停止当前服务
   # 停止 Unsloth 服务 (Ctrl+C)

3️⃣ 执行升级
   ./upgrade_cuda.sh

4️⃣ 测试新环境
   python test_vllm_cuda12.py

5️⃣ 如果有问题，回滚
   ./rollback_cuda.sh

⚠️ 风险评估:
✅ 低风险: 驱动支持 CUDA 12.4
✅ 中等风险: 可能需要重新编译某些包
❌ 高风险: 可能影响其他 CUDA 11.8 依赖的项目

💡 建议:
- 在非生产环境先测试
- 确保有足够时间处理可能的问题
- 考虑创建新的虚拟环境而不是直接升级

🔄 回退方案:
如果升级失败，可以使用 rollback_cuda.sh 回到 CUDA 11.8
""")

def main():
    """主函数"""
    print("🔧 CUDA 12.x 升级方案生成器")
    print("="*50)
    
    # 检查当前环境
    check_current_environment()
    
    # 创建所有脚本
    create_backup_plan()
    create_upgrade_script()
    create_rollback_script()
    create_vllm_test_script()
    
    # 显示升级方案
    show_upgrade_plan()
    
    print("\n✅ 所有脚本已生成完成！")
    print("\n📁 生成的文件:")
    print("   - backup_environment.sh   (环境备份)")
    print("   - upgrade_cuda.sh         (升级脚本)")
    print("   - rollback_cuda.sh        (回滚脚本)")
    print("   - test_vllm_cuda12.py     (vLLM 测试)")

if __name__ == "__main__":
    main()
