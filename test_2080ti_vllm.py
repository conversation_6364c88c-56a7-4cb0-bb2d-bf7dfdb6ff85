#!/usr/bin/env python3
"""
测试脚本：验证 2×RTX 2080Ti 运行 Qwen3-8B 的 vLLM 配置
"""

import requests
import json
import time
import torch

def check_gpu_status():
    """检查 GPU 状态"""
    print("🔍 检查 GPU 状态...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA 不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"✅ 检测到 {gpu_count} 个 GPU")
    
    total_memory = 0
    for i in range(gpu_count):
        props = torch.cuda.get_device_properties(i)
        memory_gb = props.total_memory / (1024**3)
        total_memory += memory_gb
        print(f"   GPU {i}: {props.name}")
        print(f"   显存: {memory_gb:.1f}GB")
        print(f"   计算能力: {props.major}.{props.minor}")
    
    print(f"总显存: {total_memory:.1f}GB")
    
    # 检查是否适合运行 Qwen3-8B
    if total_memory >= 20:
        print("✅ 显存充足，可以运行 Qwen3-8B")
        return True
    else:
        print("⚠️ 显存可能不足，建议使用量化版本")
        return False

def test_model_loading(base_url="http://localhost:8000"):
    """测试模型加载"""
    print("\n🚀 测试模型加载...")
    
    # 针对 2080Ti 优化的加载配置
    load_request = {
        "model_name": "qwen3-8b",
        "gpu_memory_utilization": 0.85,  # 85% 显存使用率
        "max_model_len": 8192,  # 限制序列长度
        "tensor_parallel_size": 2,  # 双卡并行
        "dtype": "bfloat16",  # 使用 bfloat16
        "gpu_device": "0,1",  # 使用双卡
        "swap_space": 4,  # 4GB 交换空间
        "trust_remote_code": True
    }
    
    try:
        print(f"发送加载请求: {load_request}")
        response = requests.post(
            f"{base_url}/models/load",
            json=load_request,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 模型加载成功!")
            print(f"   响应: {result}")
            return True
        else:
            print(f"❌ 模型加载失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 模型加载超时 (>5分钟)")
        return False
    except Exception as e:
        print(f"❌ 模型加载异常: {e}")
        return False

def test_quick_load(base_url="http://localhost:8000"):
    """测试快速加载"""
    print("\n⚡ 测试快速加载...")
    
    quick_request = {
        "model_name": "qwen3-8b",
        "gpu_memory_utilization": 0.85,
        "tensor_parallel_size": 2,
        "gpu_device": "0,1"
    }
    
    try:
        response = requests.post(
            f"{base_url}/models/quick-load",
            json=quick_request,
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 快速加载成功!")
            print(f"   响应: {result}")
            return True
        else:
            print(f"❌ 快速加载失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 快速加载异常: {e}")
        return False

def test_generation(base_url="http://localhost:8000"):
    """测试文本生成"""
    print("\n📝 测试文本生成...")
    
    test_prompts = [
        "太阳和月亮哪个更大？请详细解释。",
        "请用中文写一首关于人工智能的诗。",
        "解释什么是深度学习，包括其基本原理和应用。"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n测试 {i}: {prompt[:30]}...")
        
        generate_request = {
            "model_name": "qwen3-8b",
            "prompt": prompt,
            "max_tokens": 512,  # 适中的生成长度
            "temperature": 0.7,
            "top_p": 0.9
        }
        
        try:
            start_time = time.time()
            
            response = requests.post(
                f"{base_url}/generate",
                json=generate_request,
                timeout=60
            )
            
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 生成成功!")
                print(f"   延迟: {result['latency']:.2f}s")
                print(f"   生成 token 数: {result['tokens_generated']}")
                print(f"   生成文本: {result['generated_text'][:100]}...")
                
                # 检查是否有截断问题
                if result['tokens_generated'] < 10:
                    print("⚠️ 生成 token 数过少，可能存在截断问题")
                
            else:
                print(f"❌ 生成失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 生成异常: {e}")

def check_server_status(base_url="http://localhost:8000"):
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print("✅ 服务器运行正常")
            print(f"   引擎: {health.get('engine', 'unknown')}")
            print(f"   vLLM 可用: {health.get('vllm_available', False)}")
            print(f"   已加载模型数: {health.get('models_loaded', 0)}")
            print(f"   GPU 数量: {health.get('gpu_count', 0)}")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        return False

def get_gpu_info(base_url="http://localhost:8000"):
    """获取 GPU 信息"""
    print("\n💾 获取 GPU 信息...")
    
    try:
        response = requests.get(f"{base_url}/gpu/info", timeout=10)
        if response.status_code == 200:
            gpu_info = response.json()
            print("✅ GPU 信息获取成功:")
            
            for gpu in gpu_info.get('gpus', []):
                print(f"   GPU {gpu['id']}: {gpu['name']}")
                print(f"   显存: {gpu['memory_total']:.1f}GB")
                print(f"   已用: {gpu['memory_used']:.1f}GB")
                print(f"   使用率: {gpu['utilization']:.1f}%")
                
        else:
            print(f"❌ 获取 GPU 信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取 GPU 信息异常: {e}")

def main():
    """主测试函数"""
    print("🧪 2×RTX 2080Ti vLLM Qwen3-8B 测试")
    print("=" * 60)
    
    # 1. 检查本地 GPU 状态
    gpu_ok = check_gpu_status()
    
    # 2. 检查服务器状态
    server_ok = check_server_status()
    
    if not server_ok:
        print("\n❌ 服务器未运行，请先启动:")
        print("   python app_vllm_real.py")
        return
    
    # 3. 获取 GPU 信息
    get_gpu_info()
    
    # 4. 测试模型加载
    if test_model_loading():
        # 5. 测试文本生成
        test_generation()
    else:
        print("\n💡 如果加载失败，可以尝试:")
        print("1. 降低 gpu_memory_utilization 到 0.75")
        print("2. 减少 max_model_len 到 4096")
        print("3. 使用单卡模式 (tensor_parallel_size=1)")
        print("4. 检查模型文件是否存在于 ./models/qwen3-8b/")
    
    print("\n" + "=" * 60)
    print("测试完成!")

if __name__ == "__main__":
    main()
