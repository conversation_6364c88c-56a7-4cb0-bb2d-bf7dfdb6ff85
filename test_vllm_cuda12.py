#!/usr/bin/env python3
"""
CUDA 12.x 升级后的 vLLM 测试
"""

def test_vllm_installation():
    """测试 vLLM 安装"""
    print("📦 测试 vLLM 安装...")
    
    try:
        # 尝试安装 vLLM
        import subprocess
        import sys
        
        print("正在安装 vLLM...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "vllm"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ vLLM 安装成功")
        else:
            print(f"❌ vLLM 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ vLLM 安装异常: {e}")
        return False
    
    # 测试导入
    try:
        from vllm import LLM, SamplingParams
        print("✅ vLLM 导入成功")
        return True
    except Exception as e:
        print(f"❌ vLLM 导入失败: {e}")
        return False

def test_vllm_with_model():
    """测试 vLLM 加载模型"""
    print("🚀 测试 vLLM 模型加载...")
    
    try:
        from vllm import LLM, SamplingParams
        
        # 使用较小的配置测试
        llm = LLM(
            model="./models/qwen3-8b",
            tensor_parallel_size=2,
            gpu_memory_utilization=0.85,
            max_model_len=4096,
            trust_remote_code=True,
            dtype="bfloat16"
        )
        
        print("✅ vLLM 模型加载成功")
        
        # 简单测试生成
        sampling_params = SamplingParams(temperature=0.7, top_p=0.9, max_tokens=100)
        outputs = llm.generate(["你好，请介绍一下自己。"], sampling_params)
        
        for output in outputs:
            generated_text = output.outputs[0].text
            print(f"✅ 生成测试成功: {generated_text[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ vLLM 模型测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 vLLM CUDA 12.x 兼容性测试")
    print("=" * 50)
    
    if test_vllm_installation():
        test_vllm_with_model()
    
    print("=" * 50)
    print("测试完成！")
