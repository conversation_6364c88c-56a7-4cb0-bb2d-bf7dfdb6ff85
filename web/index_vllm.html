<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vLLM MAAS Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .model-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .model-item {
            background: white;
            margin-bottom: 15px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .model-info {
            flex: 1;
        }

        .model-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .model-details {
            color: #666;
            font-size: 0.9em;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-loaded { background: #28a745; }
        .status-loading { background: #ffc107; }
        .status-error { background: #dc3545; }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .generate-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .generate-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }

        .engine-badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 vLLM MAAS Platform</h1>
            <p>High-Performance Model as a Service powered by vLLM <span class="engine-badge">vLLM Only</span></p>
        </div>

        <div class="content">
            <!-- System Metrics -->
            <div class="section">
                <h2>📊 System Metrics</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="cpuUsage">--</div>
                        <div class="metric-label">CPU Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="memoryUsage">--</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="gpuUsage">--</div>
                        <div class="metric-label">GPU Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="activeModels">0</div>
                        <div class="metric-label">Active Models</div>
                    </div>
                </div>
            </div>

            <!-- Available Models -->
            <div class="section">
                <h2>📁 Available Models</h2>
                <div id="availableModelsList" style="max-height: 300px; overflow-y: auto; margin-bottom: 20px;">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        Loading available models...
                    </div>
                </div>
                <button class="btn" onclick="refreshAvailableModels()" style="margin-bottom: 20px;">🔄 Refresh Models</button>
            </div>

            <!-- Quick Model Loading -->
            <div class="section">
                <h2>⚡ Quick Load Model</h2>
                <div class="form-group">
                    <label for="quickModelSelect">Select Model:</label>
                    <select id="quickModelSelect">
                        <option value="">Select a model from ./models/ directory</option>
                    </select>
                </div>

                <!-- Multi-GPU Presets -->
                <div class="form-group" id="multiGpuPresets" style="display: none;">
                    <label for="gpuPreset">🔗 Multi-GPU Presets:</label>
                    <select id="gpuPreset" onchange="applyGpuPreset()">
                        <option value="">Custom Configuration</option>
                    </select>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div class="form-group">
                        <label for="quickGpuMemory">GPU Memory Utilization:</label>
                        <input type="number" id="quickGpuMemory" value="0.6" min="0.1" max="1.0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="quickTensorParallel">Tensor Parallel Size:</label>
                        <input type="number" id="quickTensorParallel" value="1" min="1" max="8" onchange="updateGpuDeviceForTP()">
                    </div>
                    <div class="form-group">
                        <label for="quickGpuDevice">GPU Device:</label>
                        <select id="quickGpuDevice" onchange="updateTPForGpuDevice()">
                            <option value="">Auto (Default)</option>
                        </select>
                    </div>
                </div>

                <!-- Multi-GPU Info -->
                <div id="multiGpuInfo" style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; display: none;">
                    <h4 style="margin: 0 0 10px 0; color: #1976d2;">🔗 Multi-GPU Configuration</h4>
                    <div id="multiGpuDetails"></div>
                </div>

                <button class="btn" id="quickLoadButton" onclick="quickLoadModel()">⚡ Quick Load</button>
            </div>

            <!-- Advanced Model Loading -->
            <div class="section">
                <h2>🔧 Advanced Model Loading</h2>
                <div class="form-group">
                    <label for="modelName">Model Name:</label>
                    <input type="text" id="modelName" placeholder="e.g., qwen3-8b">
                </div>
                <div class="form-group">
                    <label for="modelPath">Model Path (optional):</label>
                    <input type="text" id="modelPath" placeholder="Leave empty to auto-detect in ./models/ or specify custom path">
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group">
                        <label for="gpuMemory">GPU Memory Utilization:</label>
                        <input type="number" id="gpuMemory" value="0.6" min="0.1" max="1.0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="tensorParallel">Tensor Parallel Size:</label>
                        <input type="number" id="tensorParallel" value="1" min="1" max="8">
                    </div>
                    <div class="form-group">
                        <label for="gpuDevice">GPU Device:</label>
                        <select id="gpuDevice">
                            <option value="">Auto (Default)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="maxModelLen">Max Model Length (optional):</label>
                        <input type="number" id="maxModelLen" placeholder="e.g., 4096">
                    </div>
                </div>
                <button class="btn" id="loadButton" onclick="loadModel()">🔧 Advanced Load</button>
            </div>

            <!-- Model Management -->
            <div class="section">
                <h2>🎛️ Model Management</h2>
                <div class="model-list" id="modelList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        Loading models...
                    </div>
                </div>
            </div>

            <!-- Text Generation -->
            <div class="section">
                <h2>💬 Text Generation</h2>
                <div class="form-group">
                    <label for="selectedModel">Select Model:</label>
                    <select id="selectedModel">
                        <option value="">No models loaded</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="promptText">Prompt:</label>
                    <textarea id="promptText" rows="4" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; resize: vertical;" placeholder="Enter your prompt here..."></textarea>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div class="form-group">
                        <label for="maxTokens">Max Tokens:</label>
                        <input type="number" id="maxTokens" value="2048" min="1" max="4096">
                    </div>
                    <div class="form-group">
                        <label for="temperature">Temperature:</label>
                        <input type="number" id="temperature" value="0.7" min="0.0" max="2.0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="topP">Top P:</label>
                        <input type="number" id="topP" value="0.9" min="0.0" max="1.0" step="0.1">
                    </div>
                </div>
                <button class="btn" id="generateButton" onclick="generateText()">Generate Text</button>
                <div class="generate-output" id="generateOutput">Generated text will appear here...</div>
            </div>
        </div>
    </div>

    <script>
        let metricsInterval;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadModelList();
            loadMetrics();
            loadAvailableModels();
            loadGpuInfo();
            startMetricsUpdates();
        });

        function startMetricsUpdates() {
            metricsInterval = setInterval(() => {
                loadMetrics();
                loadModelList();
                loadGpuInfo();
            }, 5000); // Update every 5 seconds
        }

        async function loadMetrics() {
            try {
                const response = await fetch('/metrics');
                const metrics = await response.json();
                
                // Update system metrics
                if (metrics.system) {
                    document.getElementById('cpuUsage').textContent = `${metrics.system.cpu_percent?.toFixed(1) || 0}%`;
                    document.getElementById('memoryUsage').textContent = `${metrics.system.memory_percent?.toFixed(1) || 0}%`;
                }
                
                // Update GPU metrics
                if (metrics.gpu?.gpus && metrics.gpu.gpus.length > 0) {
                    const avgGpuUtil = metrics.gpu.gpus.reduce((sum, gpu) => sum + gpu.gpu_utilization, 0) / metrics.gpu.gpus.length;
                    document.getElementById('gpuUsage').textContent = `${avgGpuUtil.toFixed(1)}%`;
                } else {
                    document.getElementById('gpuUsage').textContent = '--';
                }
                
                // Update model count
                const modelCount = Object.keys(metrics.models || {}).length;
                document.getElementById('activeModels').textContent = modelCount;
                
            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }

        async function loadModelList() {
            try {
                const response = await fetch('/models/info');
                const models = await response.json();
                
                const modelList = document.getElementById('modelList');
                const selectedModel = document.getElementById('selectedModel');
                
                if (models.length === 0) {
                    modelList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No models loaded</div>';
                    selectedModel.innerHTML = '<option value="">No models loaded</option>';
                    return;
                }
                
                // Update model list
                modelList.innerHTML = '';
                selectedModel.innerHTML = '';
                
                models.forEach(model => {
                    const statusClass = model.status === 'loaded' ? 'status-loaded' : 
                                      model.status === 'loading' ? 'status-loading' : 'status-error';
                    
                    const modelItem = document.createElement('div');
                    modelItem.className = 'model-item';
                    modelItem.innerHTML = `
                        <div class="model-info">
                            <div class="model-name">
                                <span class="status-indicator ${statusClass}"></span>
                                ${model.name}
                            </div>
                            <div class="model-details">
                                Path: ${model.model_path} |
                                Requests: ${model.total_requests} |
                                Avg Latency: ${model.avg_latency.toFixed(3)}s |
                                Tensor Parallel: ${model.tensor_parallel_size} |
                                GPU Device: ${model.gpu_device || 'auto'}
                            </div>
                        </div>
                        <button class="btn danger" onclick="unloadModel('${model.name}')" 
                                ${model.status !== 'loaded' ? 'disabled' : ''}>
                            Unload
                        </button>
                    `;
                    modelList.appendChild(modelItem);
                    
                    // Add to select dropdown
                    if (model.status === 'loaded') {
                        const option = document.createElement('option');
                        option.value = model.name;
                        option.textContent = model.name;
                        selectedModel.appendChild(option);
                    }
                });
                
            } catch (error) {
                console.error('Error loading model list:', error);
                document.getElementById('modelList').innerHTML =
                    '<div style="color: #dc3545; text-align: center; padding: 20px;">Error loading models</div>';
            }
        }

        async function loadAvailableModels() {
            try {
                const response = await fetch('/models/available');
                const data = await response.json();

                const availableList = document.getElementById('availableModelsList');
                const quickSelect = document.getElementById('quickModelSelect');

                if (data.available_models.length === 0) {
                    availableList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No models found in ./models/ directory</div>';
                    quickSelect.innerHTML = '<option value="">No models available</option>';
                    return;
                }

                // Update available models list
                availableList.innerHTML = '';
                quickSelect.innerHTML = '<option value="">Select a model</option>';

                data.available_models.forEach(model => {
                    // Create model card
                    const modelCard = document.createElement('div');
                    modelCard.className = 'model-item';
                    modelCard.innerHTML = `
                        <div class="model-info">
                            <div class="model-name">
                                📁 ${model.name}
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em; margin-left: 10px;">
                                    ${model.parameters}
                                </span>
                            </div>
                            <div class="model-details">
                                Size: ${model.size_human} |
                                Files: ${model.file_count} |
                                Type: ${model.model_type} |
                                Path: ${model.path}
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn" onclick="quickLoadSpecificModel('${model.name}')"
                                    style="background: #28a745;">
                                ⚡ Quick Load
                            </button>
                            <button class="btn danger" onclick="deleteModelFiles('${model.name}', '${model.size_human}')"
                                    style="background: #dc3545;">
                                🗑️ Delete
                            </button>
                        </div>
                    `;
                    availableList.appendChild(modelCard);

                    // Add to select dropdown
                    const option = document.createElement('option');
                    option.value = model.name;
                    option.textContent = `${model.name} (${model.parameters}, ${model.size_human})`;
                    quickSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading available models:', error);
                document.getElementById('availableModelsList').innerHTML =
                    '<div style="color: #dc3545; text-align: center; padding: 20px;">Error loading available models</div>';
            }
        }

        function refreshAvailableModels() {
            loadAvailableModels();
        }

        // Global variable to store GPU info
        let gpuInfo = null;

        async function loadGpuInfo() {
            try {
                const response = await fetch('/gpu/info');
                const data = await response.json();
                gpuInfo = data; // Store for later use

                const quickGpuSelect = document.getElementById('quickGpuDevice');
                const advancedGpuSelect = document.getElementById('gpuDevice');
                const gpuPresetSelect = document.getElementById('gpuPreset');

                // Clear existing options (keep Auto option)
                quickGpuSelect.innerHTML = '<option value="">Auto (Default)</option>';
                advancedGpuSelect.innerHTML = '<option value="">Auto (Default)</option>';
                gpuPresetSelect.innerHTML = '<option value="">Custom Configuration</option>';

                // Show multi-GPU presets if available
                if (data.multi_gpu_configs && data.multi_gpu_configs.length > 0) {
                    document.getElementById('multiGpuPresets').style.display = 'block';

                    data.multi_gpu_configs.forEach(config => {
                        const option = document.createElement('option');
                        option.value = JSON.stringify(config);
                        option.textContent = `${config.name} - ${config.description}`;
                        gpuPresetSelect.appendChild(option);
                    });
                }

                // Add individual GPU options
                if (data.count > 0) {
                    for (let i = 0; i < data.count; i++) {
                        const optionText = `GPU ${i}`;

                        // Quick load select
                        const quickOption = document.createElement('option');
                        quickOption.value = i.toString();
                        quickOption.textContent = optionText;
                        quickGpuSelect.appendChild(quickOption);

                        // Advanced load select
                        const advancedOption = document.createElement('option');
                        advancedOption.value = i.toString();
                        advancedOption.textContent = optionText;
                        advancedGpuSelect.appendChild(advancedOption);
                    }

                    // Add multi-GPU options
                    if (data.count > 1) {
                        // Dual GPU option
                        const dualGpuOption = document.createElement('option');
                        dualGpuOption.value = '0,1';
                        dualGpuOption.textContent = '🔗 Dual GPU (0,1) - Tensor Parallel';
                        quickGpuSelect.appendChild(dualGpuOption);
                        advancedGpuSelect.appendChild(dualGpuOption.cloneNode(true));

                        // All GPUs option if more than 2
                        if (data.count >= 4) {
                            const allGpuOption = document.createElement('option');
                            const allGpuIds = Array.from({length: data.count}, (_, i) => i).join(',');
                            allGpuOption.value = allGpuIds;
                            allGpuOption.textContent = `🔗 All GPUs (${allGpuIds}) - Max Parallel`;
                            advancedGpuSelect.appendChild(allGpuOption);
                        }
                    }
                }

            } catch (error) {
                console.error('Error loading GPU info:', error);
            }
        }

        function applyGpuPreset() {
            const presetSelect = document.getElementById('gpuPreset');
            const selectedValue = presetSelect.value;

            if (selectedValue) {
                try {
                    const config = JSON.parse(selectedValue);

                    // Apply configuration
                    document.getElementById('quickGpuDevice').value = config.gpu_device;
                    document.getElementById('quickTensorParallel').value = config.tensor_parallel_size;

                    // Show multi-GPU info
                    showMultiGpuInfo(config);

                } catch (error) {
                    console.error('Error applying GPU preset:', error);
                }
            } else {
                hideMultiGpuInfo();
            }
        }

        function updateGpuDeviceForTP() {
            const tpSize = parseInt(document.getElementById('quickTensorParallel').value);
            const gpuDeviceSelect = document.getElementById('quickGpuDevice');

            if (tpSize > 1 && gpuInfo && gpuInfo.count >= tpSize) {
                // Auto-select appropriate GPU configuration
                const gpuIds = Array.from({length: tpSize}, (_, i) => i).join(',');
                gpuDeviceSelect.value = gpuIds;

                showMultiGpuInfo({
                    name: `${tpSize}-GPU Parallel`,
                    gpu_device: gpuIds,
                    tensor_parallel_size: tpSize,
                    description: `Using ${tpSize} GPUs for tensor parallelism`,
                    performance: `${tpSize}x memory capacity, faster inference`
                });
            } else if (tpSize === 1) {
                hideMultiGpuInfo();
            }
        }

        function updateTPForGpuDevice() {
            const gpuDevice = document.getElementById('quickGpuDevice').value;

            if (gpuDevice && gpuDevice.includes(',')) {
                // Multi-GPU selected, update tensor parallel size
                const gpuCount = gpuDevice.split(',').length;
                document.getElementById('quickTensorParallel').value = gpuCount;

                showMultiGpuInfo({
                    name: `${gpuCount}-GPU Parallel`,
                    gpu_device: gpuDevice,
                    tensor_parallel_size: gpuCount,
                    description: `Using GPUs ${gpuDevice} for tensor parallelism`,
                    performance: `${gpuCount}x memory capacity, faster inference`
                });
            } else {
                document.getElementById('quickTensorParallel').value = 1;
                hideMultiGpuInfo();
            }
        }

        function showMultiGpuInfo(config) {
            const infoDiv = document.getElementById('multiGpuInfo');
            const detailsDiv = document.getElementById('multiGpuDetails');

            detailsDiv.innerHTML = `
                <p><strong>Configuration:</strong> ${config.name}</p>
                <p><strong>GPUs:</strong> ${config.gpu_device}</p>
                <p><strong>Tensor Parallel Size:</strong> ${config.tensor_parallel_size}</p>
                <p><strong>Description:</strong> ${config.description}</p>
                <p><strong>Performance:</strong> ${config.performance}</p>
                <p><strong>Memory:</strong> ${config.memory_per_gpu || 'Distributed across GPUs'}</p>
            `;

            infoDiv.style.display = 'block';
        }

        function hideMultiGpuInfo() {
            document.getElementById('multiGpuInfo').style.display = 'none';
        }

        async function quickLoadSpecificModel(modelName) {
            const quickSelect = document.getElementById('quickModelSelect');
            quickSelect.value = modelName;
            await quickLoadModel();
        }

        async function quickLoadModel() {
            const selectedModel = document.getElementById('quickModelSelect').value;

            if (!selectedModel) {
                alert('Please select a model first');
                return;
            }

            const quickLoadButton = document.getElementById('quickLoadButton');
            quickLoadButton.disabled = true;
            quickLoadButton.innerHTML = '<span class="loading"></span> Loading...';

            try {
                const requestData = {
                    model_name: selectedModel,
                    gpu_memory_utilization: parseFloat(document.getElementById('quickGpuMemory').value),
                    tensor_parallel_size: parseInt(document.getElementById('quickTensorParallel').value)
                };

                const gpuDevice = document.getElementById('quickGpuDevice').value;
                if (gpuDevice) {
                    requestData.gpu_device = gpuDevice;
                }

                const response = await fetch('/models/quick-load', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`✅ ${result.message}`);
                    // Refresh model list
                    loadModelList();
                } else {
                    alert(`❌ Error: ${result.detail}`);
                }

            } catch (error) {
                alert(`❌ Network error: ${error.message}`);
            } finally {
                quickLoadButton.disabled = false;
                quickLoadButton.innerHTML = '⚡ Quick Load';
            }
        }

        async function deleteModelFiles(modelName, modelSize) {
            // Show detailed confirmation dialog
            const confirmMessage = `⚠️ 确认删除模型？

模型名称: ${modelName}
文件大小: ${modelSize}

⚠️ 警告: 此操作将永久删除模型文件，无法恢复！

确定要继续吗？`;

            if (!confirm(confirmMessage)) {
                return;
            }

            // Second confirmation for safety
            const secondConfirm = prompt(`为了安全起见，请输入模型名称 "${modelName}" 来确认删除:`);
            if (secondConfirm !== modelName) {
                alert('❌ 模型名称不匹配，删除操作已取消');
                return;
            }

            try {
                const response = await fetch(`/models/files/${modelName}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`✅ 删除成功！

模型: ${result.model_name}
路径: ${result.deleted_path}
大小: ${result.deleted_size}
文件数: ${result.deleted_files}

模型文件已从磁盘中永久删除。`);

                    // Refresh available models list
                    loadAvailableModels();
                    // Also refresh loaded models list in case it was loaded
                    loadModelList();
                } else {
                    alert(`❌ 删除失败: ${result.detail}`);
                }

            } catch (error) {
                alert(`❌ 网络错误: ${error.message}`);
            }
        }

        async function loadModel() {
            const modelName = document.getElementById('modelName').value.trim();
            const modelPath = document.getElementById('modelPath').value.trim();

            if (!modelName || !modelPath) {
                alert('Please enter both model name and path');
                return;
            }

            const loadButton = document.getElementById('loadButton');
            loadButton.disabled = true;
            loadButton.innerHTML = '<span class="loading"></span> Loading...';

            try {
                const requestData = {
                    model_name: modelName,
                    model_path: modelPath,
                    gpu_memory_utilization: parseFloat(document.getElementById('gpuMemory').value),
                    tensor_parallel_size: parseInt(document.getElementById('tensorParallel').value)
                };

                const maxModelLen = document.getElementById('maxModelLen').value.trim();
                if (maxModelLen) {
                    requestData.max_model_len = parseInt(maxModelLen);
                }

                const gpuDevice = document.getElementById('gpuDevice').value;
                if (gpuDevice) {
                    requestData.gpu_device = gpuDevice;
                }

                const response = await fetch('/models/load', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`✅ ${result.message}`);
                    // Clear form
                    document.getElementById('modelName').value = '';
                    document.getElementById('modelPath').value = '';
                    // Refresh model list
                    loadModelList();
                } else {
                    alert(`❌ Error: ${result.detail}`);
                }

            } catch (error) {
                alert(`❌ Network error: ${error.message}`);
            } finally {
                loadButton.disabled = false;
                loadButton.innerHTML = 'Load Model';
            }
        }

        async function unloadModel(modelName) {
            if (!confirm(`Are you sure you want to unload model "${modelName}"?`)) {
                return;
            }

            try {
                const response = await fetch(`/models/${modelName}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    alert(`✅ ${result.message}`);
                    loadModelList();
                } else {
                    alert(`❌ Error: ${result.detail}`);
                }

            } catch (error) {
                alert(`❌ Network error: ${error.message}`);
            }
        }

        async function generateText() {
            const selectedModel = document.getElementById('selectedModel').value;
            const promptText = document.getElementById('promptText').value.trim();

            if (!selectedModel) {
                alert('Please select a model first');
                return;
            }

            if (!promptText) {
                alert('Please enter a prompt');
                return;
            }

            const generateButton = document.getElementById('generateButton');
            const outputDiv = document.getElementById('generateOutput');

            generateButton.disabled = true;
            generateButton.innerHTML = '<span class="loading"></span> Generating...';
            outputDiv.textContent = 'Generating text...';

            try {
                const requestData = {
                    model_name: selectedModel,
                    prompt: promptText,
                    max_tokens: parseInt(document.getElementById('maxTokens').value),
                    temperature: parseFloat(document.getElementById('temperature').value),
                    top_p: parseFloat(document.getElementById('topP').value)
                };

                const startTime = Date.now();

                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    const endTime = Date.now();
                    const clientLatency = (endTime - startTime) / 1000;

                    outputDiv.innerHTML = `
<strong>Generated Text:</strong>
${result.generated_text}

<strong>Statistics:</strong>
• Tokens Generated: ${result.tokens_generated}
• Server Latency: ${result.latency.toFixed(3)}s
• Client Latency: ${clientLatency.toFixed(3)}s
• Model: ${result.model_name}
                    `;
                } else {
                    outputDiv.textContent = `❌ Error: ${result.detail}`;
                }

            } catch (error) {
                outputDiv.textContent = `❌ Network error: ${error.message}`;
            } finally {
                generateButton.disabled = false;
                generateButton.innerHTML = 'Generate Text';
            }
        }
    </script>
</body>
</html>
