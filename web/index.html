<!DOCTYPE html>
<html>
<head>
    <title>vLLM MAAS Platform</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .section {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .full-width { grid-column: 1 / -1; }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea { height: 120px; resize: vertical; }

        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px 4px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover { background: #5a6fd8; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        button.danger { background: #e74c3c; }
        button.danger:hover { background: #c0392b; }

        .response {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-loaded { background: #28a745; }
        .status-loading { background: #ffc107; animation: pulse 1s infinite; }
        .status-error { background: #dc3545; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }

        .metric-label {
            color: #666;
            font-size: 0.9em;
        }

        .model-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .model-item:last-child { border-bottom: none; }

        .model-info {
            flex-grow: 1;
        }

        .model-name {
            font-weight: bold;
            color: #333;
        }

        .model-details {
            font-size: 0.9em;
            color: #666;
            margin-top: 4px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .advanced-options {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .toggle-advanced {
            background: none;
            border: 1px solid #667eea;
            color: #667eea;
            padding: 8px 16px;
            font-size: 12px;
        }

        .toggle-advanced:hover {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>vLLM MAAS Platform</h1>
            <p>Model as a Service Management Platform</p>
        </div>

        <div class="main-content">
            <!-- System Status -->
            <div class="section">
                <h2>System Status</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="cpuUsage">--</div>
                        <div class="metric-label">CPU Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="memoryUsage">--</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="gpuUsage">--</div>
                        <div class="metric-label">GPU Usage</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="activeModels">--</div>
                        <div class="metric-label">Active Models</div>
                    </div>
                </div>
            </div>

            <!-- Model Management -->
            <div class="section">
                <h2>Model Management</h2>
                <div class="model-list" id="modelList">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        Loading models...
                    </div>
                </div>
            </div>

            <!-- Engine Status -->
            <div class="section">
                <h2>Inference Engines</h2>
                <div id="engineStatus">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        Loading engine status...
                    </div>
                </div>

                <!-- Engine Switching Control Panel -->
                <div id="engineControl" style="margin-top: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                    <h3 style="margin-top: 0; display: flex; align-items: center;">
                        🔄 Inference Engine Switcher
                        <span id="currentEngineBadge" style="margin-left: 10px; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: normal;">
                            Detecting...
                        </span>
                        <span style="margin-left: 10px; font-size: 0.8em; color: #666;">
                            (GPU Memory: 22GB Total)
                        </span>
                    </h3>

                    <div style="margin-bottom: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; font-size: 0.9em;">
                        ⚠️ <strong>GPU Memory Limitation:</strong> With 2×RTX 2080Ti (22GB total), only one engine can run at a time to avoid memory conflicts.
                    </div>

                    <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
                        <button id="switchToVllmBtn" onclick="switchToEngine('vllm')"
                                style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            🚀 Switch to vLLM
                            <div style="font-size: 0.8em; font-weight: normal;">High Performance GPU Inference</div>
                        </button>
                        <button id="switchToOllamaBtn" onclick="switchToEngine('ollama')"
                                style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            🦙 Switch to Ollama
                            <div style="font-size: 0.8em; font-weight: normal;">Easy Local Model Management</div>
                        </button>
                        <button onclick="stopAllEngines()"
                                style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            ⏹️ Stop All Engines
                            <div style="font-size: 0.8em; font-weight: normal;">Free GPU Memory</div>
                        </button>
                        <button onclick="checkEngineStatus()"
                                style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                            🔍 Check Status
                            <div style="font-size: 0.8em; font-weight: normal;">Refresh Engine Status</div>
                        </button>
                    </div>

                    <div id="engineSwitchLog" style="background: #000; color: #0f0; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 0.9em; max-height: 200px; overflow-y: auto; display: none;">
                        <!-- Command output will appear here -->
                    </div>
                </div>

                <div id="ollamaModels" style="margin-top: 15px; display: none;">
                    <h3>Available Ollama Models</h3>
                    <div id="ollamaModelList" style="max-height: 200px; overflow-y: auto; border: 1px solid #eee; padding: 10px; border-radius: 4px;">
                        Loading...
                    </div>
                </div>
            </div>


            <!-- Load New Model -->
            <div class="section full-width">
                <h2>Load New Model</h2>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                    <input type="text" id="modelName" placeholder="Model Name (e.g., llama2-7b)">
                    <input type="text" id="modelPath" placeholder="Model Path or HuggingFace ID">
                    <select id="engineSelect">
                        <option value="auto">Auto-detect Engine</option>
                        <option value="vllm">vLLM Engine</option>
                        <option value="ollama">Ollama Engine</option>
                    </select>
                </div>

                <button class="toggle-advanced" onclick="toggleAdvanced()">Advanced Options</button>

                <div class="advanced-options" id="advancedOptions">
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <label>GPU Memory Utilization:</label>
                            <input type="number" id="gpuMemory" value="0.9" min="0.1" max="1.0" step="0.1">
                        </div>
                        <div>
                            <label>Max Model Length:</label>
                            <input type="number" id="maxModelLen" placeholder="Auto">
                        </div>
                        <div>
                            <label>Tensor Parallel Size:</label>
                            <input type="number" id="tensorParallel" value="1" min="1">
                        </div>
                    </div>
                </div>

                <button onclick="loadModel()" id="loadButton">Load Model</button>
            </div>

            <!-- Text Generation -->
            <div class="section full-width">
                <h2>Text Generation</h2>
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                    <div>
                        <label>Select Model:</label>
                        <select id="selectedModel">
                            <option value="">No models loaded</option>
                        </select>

                        <label>Generation Parameters:</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <div>
                                <label>Max Tokens:</label>
                                <input type="number" id="maxTokens" value="2048" min="1">
                            </div>
                            <div>
                                <label>Temperature:</label>
                                <input type="number" id="temperature" value="0.7" min="0" max="2" step="0.1">
                            </div>
                        </div>
                        <div>
                            <label>Top P:</label>
                            <input type="number" id="topP" value="0.9" min="0" max="1" step="0.1">
                        </div>

                        <button onclick="generateText()" id="generateButton">Generate Text</button>
                        <button onclick="clearResponse()" style="background: #6c757d;">Clear</button>
                    </div>

                    <div>
                        <label>Prompt:</label>
                        <textarea id="prompt" placeholder="Enter your prompt here..."></textarea>

                        <label>Response:</label>
                        <div id="response" class="response">Generated text will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let metricsInterval;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadModelList();
            loadMetrics();
            loadEngineStatus();
            updateEngineStatus();
            startMetricsUpdates();
        });

        function startMetricsUpdates() {
            metricsInterval = setInterval(() => {
                loadMetrics();
                loadEngineStatus();
            }, 5000); // Update every 5 seconds
        }

        async function loadEngineStatus() {
            try {
                const response = await fetch('/engines');
                const engines = await response.json();

                const statusDiv = document.getElementById('engineStatus');
                statusDiv.innerHTML = '';

                Object.entries(engines).forEach(([engine, available]) => {
                    const engineDiv = document.createElement('div');
                    engineDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 8px; margin: 4px 0; border: 1px solid #eee; border-radius: 4px;';

                    const statusColor = available ? '#28a745' : '#dc3545';
                    const statusText = available ? 'Available' : 'Not Available';

                    engineDiv.innerHTML = `
                        <span style="font-weight: bold; text-transform: capitalize;">${engine} Engine</span>
                        <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                    `;

                    statusDiv.appendChild(engineDiv);
                });

                // Load Ollama models if Ollama is available
                if (engines.ollama) {
                    loadOllamaModels();
                }

            } catch (error) {
                console.error('Error loading engine status:', error);
                document.getElementById('engineStatus').innerHTML =
                    '<div style="color: #dc3545;">Error loading engine status</div>';
            }
        }

        async function loadOllamaModels() {
            try {
                const response = await fetch('/models/ollama/available');
                const data = await response.json();

                const ollamaDiv = document.getElementById('ollamaModels');
                const modelListDiv = document.getElementById('ollamaModelList');

                if (data.models && data.models.length > 0) {
                    ollamaDiv.style.display = 'block';
                    modelListDiv.innerHTML = '';

                    data.models.forEach(model => {
                        const modelDiv = document.createElement('div');
                        modelDiv.style.cssText = 'padding: 4px 8px; border-bottom: 1px solid #f0f0f0; cursor: pointer;';
                        modelDiv.innerHTML = `
                            <span>${model}</span>
                            <button style="float: right; padding: 2px 8px; font-size: 0.8em;"
                                    onclick="quickLoadOllamaModel('${model}')">
                                Quick Load
                            </button>
                        `;
                        modelListDiv.appendChild(modelDiv);
                    });
                } else {
                    ollamaDiv.style.display = 'none';
                }

            } catch (error) {
                console.error('Error loading Ollama models:', error);
            }
        }

        function quickLoadOllamaModel(modelName) {
            document.getElementById('modelName').value = modelName;
            document.getElementById('modelPath').value = modelName;
            document.getElementById('engineSelect').value = 'ollama';

            // Scroll to load section
            document.querySelector('h2').scrollIntoView({ behavior: 'smooth' });
        }

        // Engine Switching Functions
        async function switchToEngine(targetEngine) {
            const logDiv = document.getElementById('engineSwitchLog');
            const vllmBtn = document.getElementById('switchToVllmBtn');
            const ollamaBtn = document.getElementById('switchToOllamaBtn');

            // Show log and add message
            logDiv.style.display = 'block';
            appendToSwitchLog(`🔄 Switching to ${targetEngine.toUpperCase()} engine...`);

            // Disable buttons during operation
            vllmBtn.disabled = true;
            ollamaBtn.disabled = true;

            try {
                const response = await fetch('/engine/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ target_engine: targetEngine })
                });

                const result = await response.json();

                if (response.ok) {
                    appendToSwitchLog(`✅ ${result.message}`);
                    if (result.steps && result.steps.length > 0) {
                        result.steps.forEach(step => {
                            appendToSwitchLog(`   ${step}`);
                        });
                    }
                } else {
                    appendToSwitchLog(`❌ Error: ${result.detail || result.message}`);
                }

                // Update status after switch
                setTimeout(() => {
                    updateEngineStatus();
                    loadEngineStatus();
                }, 2000);

            } catch (error) {
                appendToSwitchLog(`❌ Network error: ${error.message}`);
            } finally {
                // Re-enable buttons
                vllmBtn.disabled = false;
                ollamaBtn.disabled = false;
            }
        }

        async function stopAllEngines() {
            await executeEngineCommand('stop_all', 'Stopping all inference engines...');
        }

        async function checkEngineStatus() {
            await executeEngineCommand('status', 'Checking engine status...');
        }

        async function executeEngineCommand(action, message) {
            const logDiv = document.getElementById('engineSwitchLog');

            // Show log and add message
            logDiv.style.display = 'block';
            appendToSwitchLog(message);

            try {
                const response = await fetch('/engine/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: action })
                });

                const result = await response.json();

                if (response.ok) {
                    appendToSwitchLog(`✅ ${result.message}`);
                    if (result.output) {
                        appendToSwitchLog(result.output);
                    }
                    if (result.steps && result.steps.length > 0) {
                        result.steps.forEach(step => {
                            appendToSwitchLog(`   ${step}`);
                        });
                    }
                } else {
                    appendToSwitchLog(`❌ Error: ${result.detail || result.message}`);
                }

                // Update status after command
                setTimeout(() => {
                    updateEngineStatus();
                    loadEngineStatus();
                }, 1000);

            } catch (error) {
                appendToSwitchLog(`❌ Network error: ${error.message}`);
            }
        }

        function appendToSwitchLog(message) {
            const logDiv = document.getElementById('engineSwitchLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function updateEngineStatus() {
            try {
                const response = await fetch('/engines');
                const engines = await response.json();

                const statusBadge = document.getElementById('currentEngineBadge');
                const vllmBtn = document.getElementById('switchToVllmBtn');
                const ollamaBtn = document.getElementById('switchToOllamaBtn');

                // Determine current active engine
                let activeEngine = 'none';
                let statusText = 'No Engine Running';
                let statusColor = '#6c757d';

                if (engines.vllm && engines.ollama) {
                    activeEngine = 'both';
                    statusText = '⚠️ Both Running (Memory Conflict!)';
                    statusColor = '#dc3545';
                } else if (engines.vllm) {
                    activeEngine = 'vllm';
                    statusText = '🚀 vLLM Active';
                    statusColor = '#007bff';
                } else if (engines.ollama) {
                    activeEngine = 'ollama';
                    statusText = '🦙 Ollama Active';
                    statusColor = '#28a745';
                }

                statusBadge.textContent = statusText;
                statusBadge.style.background = statusColor;
                statusBadge.style.color = 'white';

                // Update button states
                if (activeEngine === 'vllm') {
                    vllmBtn.style.opacity = '0.6';
                    vllmBtn.style.background = '#6c757d';
                    ollamaBtn.style.opacity = '1';
                    ollamaBtn.style.background = '#28a745';
                } else if (activeEngine === 'ollama') {
                    ollamaBtn.style.opacity = '0.6';
                    ollamaBtn.style.background = '#6c757d';
                    vllmBtn.style.opacity = '1';
                    vllmBtn.style.background = '#007bff';
                } else {
                    vllmBtn.style.opacity = '1';
                    vllmBtn.style.background = '#007bff';
                    ollamaBtn.style.opacity = '1';
                    ollamaBtn.style.background = '#28a745';
                }

                // Refresh engine status and models
                loadEngineStatus();

            } catch (error) {
                console.error('Error updating engine status:', error);
                const statusBadge = document.getElementById('currentEngineBadge');
                statusBadge.textContent = 'Status Unknown';
                statusBadge.style.background = '#6c757d';
                statusBadge.style.color = 'white';
            }
        }

        async function loadMetrics() {
            try {
                const response = await fetch('/metrics');
                const metrics = await response.json();

                // Update system metrics
                document.getElementById('cpuUsage').textContent =
                    metrics.system?.cpu_percent ? `${metrics.system.cpu_percent.toFixed(1)}%` : '--';
                document.getElementById('memoryUsage').textContent =
                    metrics.system?.memory_percent ? `${metrics.system.memory_percent.toFixed(1)}%` : '--';

                // Update GPU metrics
                if (metrics.gpu?.gpus && metrics.gpu.gpus.length > 0) {
                    const avgGpuUtil = metrics.gpu.gpus.reduce((sum, gpu) => sum + gpu.gpu_utilization, 0) / metrics.gpu.gpus.length;
                    document.getElementById('gpuUsage').textContent = `${avgGpuUtil.toFixed(1)}%`;
                } else {
                    document.getElementById('gpuUsage').textContent = '--';
                }

                // Update active models count
                const activeModels = Object.keys(metrics.models || {}).length;
                document.getElementById('activeModels').textContent = activeModels;

            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }

        async function loadModelList() {
            try {
                const [modelsResponse, infoResponse] = await Promise.all([
                    fetch('/models'),
                    fetch('/models/info')
                ]);

                const models = await modelsResponse.json();
                const modelsInfo = await infoResponse.json();

                // Update model selection dropdown
                const select = document.getElementById('selectedModel');
                select.innerHTML = '';

                if (models.length === 0) {
                    select.innerHTML = '<option value="">No models loaded</option>';
                } else {
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model;
                        option.text = model;
                        select.appendChild(option);
                    });
                }

                // Update model list display
                const modelList = document.getElementById('modelList');
                modelList.innerHTML = '';

                if (modelsInfo.length === 0) {
                    modelList.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">No models loaded</div>';
                } else {
                    modelsInfo.forEach(model => {
                        const modelItem = document.createElement('div');
                        modelItem.className = 'model-item';

                        const statusClass = model.status === 'loaded' ? 'status-loaded' :
                                          model.status === 'loading' ? 'status-loading' : 'status-error';

                        modelItem.innerHTML = `
                            <div class="model-info">
                                <div class="model-name">
                                    <span class="status-indicator ${statusClass}"></span>
                                    ${model.name}
                                    <span style="background: #667eea; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; margin-left: 8px;">
                                        ${model.engine || 'unknown'}
                                    </span>
                                </div>
                                <div class="model-details">
                                    Status: ${model.status} |
                                    Engine: ${model.engine || 'unknown'} |
                                    Requests: ${model.total_requests} |
                                    Avg Latency: ${model.avg_latency.toFixed(2)}s
                                </div>
                            </div>
                            <button class="danger" onclick="unloadModel('${model.name}')"
                                    ${model.status !== 'loaded' ? 'disabled' : ''}>
                                Unload
                            </button>
                        `;

                        modelList.appendChild(modelItem);
                    });
                }

            } catch (error) {
                console.error('Error loading model list:', error);
                document.getElementById('modelList').innerHTML =
                    '<div style="text-align: center; color: #dc3545; padding: 20px;">Error loading models</div>';
            }
        }

        async function loadModel() {
            const modelName = document.getElementById('modelName').value.trim();
            const modelPath = document.getElementById('modelPath').value.trim();
            const engine = document.getElementById('engineSelect').value;

            if (!modelName || !modelPath) {
                alert('Please enter both model name and path');
                return;
            }

            const loadButton = document.getElementById('loadButton');
            loadButton.disabled = true;
            loadButton.innerHTML = '<span class="loading"></span> Loading...';

            try {
                const requestData = {
                    model_name: modelName,
                    model_path: modelPath,
                    engine: engine,
                    gpu_memory_utilization: parseFloat(document.getElementById('gpuMemory').value),
                    tensor_parallel_size: parseInt(document.getElementById('tensorParallel').value)
                };

                const maxModelLen = document.getElementById('maxModelLen').value.trim();
                if (maxModelLen) {
                    requestData.max_model_len = parseInt(maxModelLen);
                }

                const response = await fetch('/models/load', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (response.ok) {
                    alert(result.message);
                    // Clear form
                    document.getElementById('modelName').value = '';
                    document.getElementById('modelPath').value = '';
                    // Refresh model list
                    setTimeout(loadModelList, 1000);
                } else {
                    alert('Error: ' + result.detail);
                }

            } catch (error) {
                console.error('Error loading model:', error);
                alert('Error loading model: ' + error.message);
            } finally {
                loadButton.disabled = false;
                loadButton.innerHTML = 'Load Model';
            }
        }

        async function unloadModel(modelName) {
            if (!confirm(`Are you sure you want to unload model "${modelName}"?`)) {
                return;
            }

            try {
                const response = await fetch(`/models/${modelName}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    alert(result.message);
                    loadModelList();
                } else {
                    alert('Error: ' + result.detail);
                }

            } catch (error) {
                console.error('Error unloading model:', error);
                alert('Error unloading model: ' + error.message);
            }
        }

        async function generateText() {
            const modelName = document.getElementById('selectedModel').value;
            const prompt = document.getElementById('prompt').value.trim();

            if (!modelName) {
                alert('Please select a model');
                return;
            }

            if (!prompt) {
                alert('Please enter a prompt');
                return;
            }

            const generateButton = document.getElementById('generateButton');
            generateButton.disabled = true;
            generateButton.innerHTML = '<span class="loading"></span> Generating...';

            const responseDiv = document.getElementById('response');
            responseDiv.textContent = 'Generating...';

            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        model_name: modelName,
                        prompt: prompt,
                        max_tokens: parseInt(document.getElementById('maxTokens').value),
                        temperature: parseFloat(document.getElementById('temperature').value),
                        top_p: parseFloat(document.getElementById('topP').value)
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    responseDiv.textContent = result.generated_text;
                    // Refresh model list to update metrics
                    loadModelList();
                } else {
                    responseDiv.textContent = 'Error: ' + result.detail;
                }

            } catch (error) {
                console.error('Error generating text:', error);
                responseDiv.textContent = 'Error: ' + error.message;
            } finally {
                generateButton.disabled = false;
                generateButton.innerHTML = 'Generate Text';
            }
        }

        function clearResponse() {
            document.getElementById('response').textContent = 'Generated text will appear here...';
        }

        function toggleAdvanced() {
            const options = document.getElementById('advancedOptions');
            const button = event.target;

            if (options.style.display === 'none' || !options.style.display) {
                options.style.display = 'block';
                button.textContent = 'Hide Advanced Options';
            } else {
                options.style.display = 'none';
                button.textContent = 'Advanced Options';
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (metricsInterval) {
                clearInterval(metricsInterval);
            }
        });
    </script>
</body>
</html>