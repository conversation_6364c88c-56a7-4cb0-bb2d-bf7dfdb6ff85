#!/bin/bash
# CUDA 12.x 升级脚本

set -e  # 遇到错误立即退出

echo "🚀 开始 CUDA 12.x 升级..."

# 1. 卸载当前 PyTorch 相关包
echo "🗑️ 卸载当前 PyTorch 包..."
pip uninstall -y torch torchvision torchaudio

# 2. 卸载 CUDA 11.x 相关包
echo "🗑️ 卸载 CUDA 11.x 包..."
pip uninstall -y nvidia-cuda-cupti-cu11 nvidia-cuda-nvrtc-cu11 nvidia-cuda-runtime-cu11 || true

# 3. 清理缓存
echo "🧹 清理缓存..."
pip cache purge
python -c "import torch; torch.cuda.empty_cache()" 2>/dev/null || true

# 4. 安装 PyTorch CUDA 12.x 版本
echo "📦 安装 PyTorch CUDA 12.x..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124

# 5. 验证安装
echo "✅ 验证安装..."
python -c "
import torch
print(f'新环境测试:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU 数量: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f'GPU {i}: {props.name}')
"

echo "🎉 CUDA 12.x 升级完成！"
