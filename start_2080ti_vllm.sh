#!/bin/bash

# 2×RTX 2080Ti vLLM 启动脚本
# 针对 22GB 显存优化的 Qwen3-8B 推理服务

echo "🚀 启动 2×RTX 2080Ti vLLM 服务..."
echo "=========================================="

# 检查 GPU 状态
echo "🔍 检查 GPU 状态..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=index,name,memory.total,memory.used,memory.free --format=csv,noheader,nounits
else
    echo "⚠️ nvidia-smi 不可用，无法检查 GPU 状态"
fi

# 设置环境变量优化
echo "⚙️ 设置环境变量..."

# CUDA 优化
export CUDA_VISIBLE_DEVICES=0,1  # 使用双卡
export CUDA_LAUNCH_BLOCKING=0    # 异步执行
export CUDA_CACHE_DISABLE=0      # 启用 CUDA 缓存

# PyTorch 优化
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512  # 限制内存分片大小
export TORCH_CUDNN_V8_API_ENABLED=1  # 启用 cuDNN v8 API

# vLLM 优化
export VLLM_USE_MODELSCOPE=false     # 禁用 ModelScope
export VLLM_WORKER_MULTIPROC_METHOD=spawn  # 使用 spawn 方法
export VLLM_ENGINE_ITERATION_TIMEOUT_S=3600  # 增加超时时间
export VLLM_LOGGING_LEVEL=INFO       # 设置日志级别

# 内存优化
export MALLOC_TRIM_THRESHOLD_=0      # 禁用 malloc trim
export MALLOC_MMAP_THRESHOLD_=131072 # 设置 mmap 阈值

echo "✅ 环境变量设置完成"

# 检查模型文件
echo "📁 检查模型文件..."
if [ -d "./models/qwen3-8b" ]; then
    echo "✅ 找到 Qwen3-8B 模型目录"
    
    # 检查关键文件
    if [ -f "./models/qwen3-8b/config.json" ]; then
        echo "✅ config.json 存在"
    else
        echo "❌ config.json 不存在"
    fi
    
    if [ -f "./models/qwen3-8b/tokenizer.json" ]; then
        echo "✅ tokenizer.json 存在"
    else
        echo "❌ tokenizer.json 不存在"
    fi
    
    # 检查模型权重文件
    if ls ./models/qwen3-8b/*.safetensors 1> /dev/null 2>&1; then
        echo "✅ 找到 safetensors 权重文件"
        echo "   文件列表:"
        ls -lh ./models/qwen3-8b/*.safetensors | awk '{print "   " $9 " (" $5 ")"}'
    elif ls ./models/qwen3-8b/*.bin 1> /dev/null 2>&1; then
        echo "✅ 找到 bin 权重文件"
        echo "   文件列表:"
        ls -lh ./models/qwen3-8b/*.bin | awk '{print "   " $9 " (" $5 ")"}'
    else
        echo "❌ 未找到模型权重文件"
        echo "请确保模型已正确下载到 ./models/qwen3-8b/ 目录"
        exit 1
    fi
else
    echo "❌ 未找到 Qwen3-8B 模型目录: ./models/qwen3-8b"
    echo "请先下载模型或检查路径"
    exit 1
fi

# 检查 Python 依赖
echo "🐍 检查 Python 依赖..."
python3 -c "
import sys
try:
    import torch
    print(f'✅ PyTorch {torch.__version__}')
    print(f'   CUDA 可用: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'   GPU 数量: {torch.cuda.device_count()}')
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / (1024**3)
            print(f'   GPU {i}: {props.name} ({memory_gb:.1f}GB)')
except ImportError:
    print('❌ PyTorch 未安装')
    sys.exit(1)

try:
    import vllm
    print(f'✅ vLLM {vllm.__version__}')
except ImportError:
    print('❌ vLLM 未安装')
    sys.exit(1)

try:
    import fastapi
    print(f'✅ FastAPI {fastapi.__version__}')
except ImportError:
    print('❌ FastAPI 未安装')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ 依赖检查失败，请安装必要的包"
    exit 1
fi

# 清理 GPU 内存
echo "🧹 清理 GPU 内存..."
python3 -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('✅ GPU 内存已清理')
"

# 启动服务
echo "🚀 启动 vLLM 服务..."
echo "配置信息:"
echo "  - GPU: 2×RTX 2080Ti (22GB 总显存)"
echo "  - 显存使用率: 85%"
echo "  - 序列长度: 8192"
echo "  - 并行度: 2 (双卡)"
echo "  - 数据类型: bfloat16"
echo "  - 交换空间: 4GB"
echo ""
echo "🌐 Web 界面: http://localhost:8000"
echo "📚 API 文档: http://localhost:8000/docs"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================================="

# 启动应用
python3 app_vllm_real.py
