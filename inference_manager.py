"""
Unified Inference Manager for vLLM MAAS Platform
Manages multiple inference engines (vLLM, Ollama, etc.)
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from enum import Enum

from model_manager import ModelManager
from ollama_manager import OllamaManager

logger = logging.getLogger(__name__)


class InferenceEngine(str, Enum):
    """Supported inference engines."""
    VLLM = "vllm"
    OLLAMA = "ollama"
    AUTO = "auto"


class InferenceManager:
    """Unified manager for multiple inference engines."""
    
    def __init__(self, settings):
        self.settings = settings
        self.vllm_manager = ModelManager(settings)
        self.ollama_manager = OllamaManager(settings)
        self.loaded_models: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        
        # Engine availability
        self.engines_available = {
            InferenceEngine.VLLM: False,
            InferenceEngine.OLLAMA: False
        }
    
    async def initialize(self):
        """Initialize and check available engines."""
        logger.info("Initializing inference engines...")
        
        # Check vLLM availability
        try:
            import vllm
            self.engines_available[InferenceEngine.VLLM] = True
            logger.info("✅ vLLM engine available")
        except ImportError as e:
            logger.warning(f"⚠️  vLLM engine not available: {e}")
        
        # Check Ollama availability
        ollama_available = await self.ollama_manager.check_ollama_service()
        if not ollama_available:
            # Try to start Ollama service
            ollama_available = await self.ollama_manager.start_ollama_service()
        
        self.engines_available[InferenceEngine.OLLAMA] = ollama_available
        if ollama_available:
            logger.info("✅ Ollama engine available")
        else:
            logger.warning("⚠️  Ollama engine not available")
        
        # Log available engines
        available = [engine.value for engine, available in self.engines_available.items() if available]
        logger.info(f"Available inference engines: {available}")
    
    def _determine_engine(self, engine: Union[str, InferenceEngine], model_name: str = None) -> InferenceEngine:
        """Determine which engine to use."""
        if engine == InferenceEngine.AUTO:
            # Auto-select based on model name or availability
            if model_name:
                # Common Ollama models
                ollama_models = ['llama2', 'codellama', 'mistral', 'phi', 'neural-chat', 'starcode']
                if any(ollama_model in model_name.lower() for ollama_model in ollama_models):
                    if self.engines_available[InferenceEngine.OLLAMA]:
                        return InferenceEngine.OLLAMA
            
            # Default to vLLM if available, otherwise Ollama
            if self.engines_available[InferenceEngine.VLLM]:
                return InferenceEngine.VLLM
            elif self.engines_available[InferenceEngine.OLLAMA]:
                return InferenceEngine.OLLAMA
            else:
                raise ValueError("No inference engines available")
        
        # Validate requested engine is available
        engine_enum = InferenceEngine(engine)
        if not self.engines_available[engine_enum]:
            raise ValueError(f"Inference engine '{engine}' is not available")
        
        return engine_enum
    
    async def load_model(
        self,
        model_name: str,
        model_path: str = None,
        engine: Union[str, InferenceEngine] = InferenceEngine.AUTO,
        **kwargs
    ) -> bool:
        """Load a model using specified or auto-detected engine."""
        async with self._lock:
            if model_name in self.loaded_models:
                logger.warning(f"Model {model_name} is already loaded")
                return True
            
            # Determine engine to use
            selected_engine = self._determine_engine(engine, model_path or model_name)
            
            try:
                logger.info(f"Loading model {model_name} with {selected_engine.value} engine")
                
                # Track model loading
                self.loaded_models[model_name] = {
                    "status": "loading",
                    "engine": selected_engine.value,
                    "model_path": model_path or model_name
                }
                
                # Load with appropriate engine
                if selected_engine == InferenceEngine.VLLM:
                    success = await self.vllm_manager.load_model(
                        model_name, model_path or model_name, **kwargs
                    )
                elif selected_engine == InferenceEngine.OLLAMA:
                    success = await self.ollama_manager.load_model(
                        model_path or model_name, **kwargs
                    )
                else:
                    raise ValueError(f"Unsupported engine: {selected_engine}")
                
                if success:
                    self.loaded_models[model_name]["status"] = "loaded"
                    logger.info(f"Successfully loaded model {model_name} with {selected_engine.value}")
                    return True
                else:
                    # Clean up on failure
                    del self.loaded_models[model_name]
                    return False
                    
            except Exception as e:
                logger.error(f"Error loading model {model_name}: {e}")
                if model_name in self.loaded_models:
                    del self.loaded_models[model_name]
                raise e
    
    async def unload_model(self, model_name: str) -> bool:
        """Unload a model."""
        async with self._lock:
            if model_name not in self.loaded_models:
                logger.warning(f"Model {model_name} is not loaded")
                return False
            
            model_info = self.loaded_models[model_name]
            engine = model_info["engine"]
            
            try:
                if engine == InferenceEngine.VLLM:
                    success = await self.vllm_manager.unload_model(model_name)
                elif engine == InferenceEngine.OLLAMA:
                    success = await self.ollama_manager.unload_model(model_info["model_path"])
                else:
                    raise ValueError(f"Unknown engine: {engine}")
                
                if success:
                    del self.loaded_models[model_name]
                    logger.info(f"Successfully unloaded model {model_name}")
                
                return success
                
            except Exception as e:
                logger.error(f"Error unloading model {model_name}: {e}")
                raise e
    
    async def generate(
        self,
        model_name: str,
        prompt: str,
        max_tokens: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate text using the appropriate engine."""
        if model_name not in self.loaded_models:
            raise ValueError(f"Model {model_name} is not loaded")
        
        model_info = self.loaded_models[model_name]
        if model_info["status"] != "loaded":
            raise ValueError(f"Model {model_name} is not ready (status: {model_info['status']})")
        
        engine = model_info["engine"]
        
        try:
            if engine == InferenceEngine.VLLM:
                result = await self.vllm_manager.generate(
                    model_name, prompt, max_tokens, temperature, top_p, stream, **kwargs
                )
            elif engine == InferenceEngine.OLLAMA:
                result = await self.ollama_manager.generate(
                    model_info["model_path"], prompt, max_tokens, temperature, top_p, stream, **kwargs
                )
            else:
                raise ValueError(f"Unknown engine: {engine}")
            
            # Add engine info to result
            result["engine"] = engine
            return result
            
        except Exception as e:
            logger.error(f"Error generating with model {model_name}: {e}")
            raise e
    
    async def list_loaded_models(self) -> List[str]:
        """List all loaded model names."""
        return [name for name, info in self.loaded_models.items() if info["status"] == "loaded"]
    
    async def get_models_info(self) -> List[Dict[str, Any]]:
        """Get detailed information about all loaded models."""
        models_info = []
        
        for name, info in self.loaded_models.items():
            model_info = {
                "name": name,
                "status": info["status"],
                "engine": info["engine"],
                "model_path": info.get("model_path", ""),
                "total_requests": 0,
                "avg_latency": 0.0
            }
            
            # Get engine-specific info
            try:
                if info["engine"] == InferenceEngine.VLLM:
                    vllm_info = await self.vllm_manager.get_model_info(name)
                    if vllm_info:
                        model_info.update(vllm_info)
                elif info["engine"] == InferenceEngine.OLLAMA:
                    ollama_info = await self.ollama_manager.get_model_info(info["model_path"])
                    if ollama_info:
                        model_info.update(ollama_info)
            except Exception as e:
                logger.warning(f"Error getting info for model {name}: {e}")
            
            models_info.append(model_info)
        
        return models_info
    
    async def get_available_engines(self) -> Dict[str, bool]:
        """Get status of available engines."""
        return {engine.value: available for engine, available in self.engines_available.items()}
    
    async def list_ollama_models(self) -> List[str]:
        """List available Ollama models."""
        if self.engines_available[InferenceEngine.OLLAMA]:
            return await self.ollama_manager.list_available_models()
        return []
    
    async def shutdown(self):
        """Shutdown all engines and clean up resources."""
        logger.info("Shutting down inference manager...")
        
        # Shutdown individual managers
        await self.vllm_manager.shutdown()
        await self.ollama_manager.shutdown()
        
        # Clear loaded models
        self.loaded_models.clear()
        
        logger.info("Inference manager shutdown complete")
