#!/usr/bin/env python3
"""
Multi-Engine vLLM MAAS Platform for testing
Supports both vLLM and Ollama engines with mock responses when engines are not available.
"""

import asyncio
import logging
import time
import psutil
import subprocess
import os
from typing import Dict, List, Optional
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel

# Try to import GPU monitoring
try:
    import pynvml
    NVIDIA_ML_AVAILABLE = True
except ImportError:
    NVIDIA_ML_AVAILABLE = False
    logging.warning("pynvml not available, GPU monitoring disabled")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="vLLM MAAS Platform (Multi-Engine)",
    description="Multi-Engine Model as a Service platform - Testing mode with vLLM + Ollama support",
    version="1.1.0-multi"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data for testing
mock_models = {}
available_engines = {
    "vllm": False,  # Will be detected at startup
    "ollama": False  # Will be detected at startup
}

class ModelLoadRequest(BaseModel):
    model_name: str
    model_path: str
    engine: str = "auto"
    gpu_memory_utilization: float = 0.9
    max_model_len: Optional[int] = None
    tensor_parallel_size: int = 1

class GenerateRequest(BaseModel):
    model_name: str
    prompt: str
    max_tokens: int = 2048
    temperature: float = 0.7
    top_p: float = 0.9
    stream: bool = False

class GenerateResponse(BaseModel):
    generated_text: str
    tokens_generated: int
    latency: float
    engine: str

class ModelInfo(BaseModel):
    name: str
    status: str
    engine: str
    gpu_memory_usage: float = 0.0
    total_requests: int = 0
    avg_latency: float = 0.0

class OllamaControlRequest(BaseModel):
    action: str  # start, stop, restart, status

class EngineSwitchRequest(BaseModel):
    target_engine: str  # vllm, ollama

class EngineControlRequest(BaseModel):
    action: str  # stop_all, status

def initialize_gpu_monitoring():
    """Initialize GPU monitoring."""
    global gpu_count
    gpu_count = 0

    if NVIDIA_ML_AVAILABLE:
        try:
            pynvml.nvmlInit()
            gpu_count = pynvml.nvmlDeviceGetCount()
            logger.info(f"Initialized GPU monitoring for {gpu_count} GPUs")
        except Exception as e:
            logger.warning(f"Failed to initialize NVIDIA ML: {e}")
            gpu_count = 0

    return gpu_count > 0

def get_gpu_metrics():
    """Get current GPU metrics."""
    if not NVIDIA_ML_AVAILABLE or gpu_count == 0:
        return None

    try:
        gpu_metrics = []
        for i in range(gpu_count):
            handle = pynvml.nvmlDeviceGetHandleByIndex(i)

            # GPU utilization
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)

            # Memory info
            mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)

            # Temperature
            temp = pynvml.nvmlDeviceGetTemperature(handle, pynvml.NVML_TEMPERATURE_GPU)

            # Power usage
            try:
                power = pynvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # Convert to watts
            except:
                power = 0

            gpu_metrics.append({
                "gpu_id": i,
                "gpu_utilization": util.gpu,
                "memory_utilization": util.memory,
                "memory_total": mem_info.total,
                "memory_used": mem_info.used,
                "memory_free": mem_info.free,
                "memory_percent": (mem_info.used / mem_info.total) * 100,
                "temperature": temp,
                "power_usage": power
            })

        return {
            "gpus": gpu_metrics,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Error collecting GPU metrics: {e}")
        return None

def get_system_metrics():
    """Get current system metrics."""
    try:
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=None)
        cpu_count_logical = psutil.cpu_count()

        # Memory metrics
        memory = psutil.virtual_memory()

        # Disk metrics
        disk = psutil.disk_usage('/')

        return {
            "cpu_percent": cpu_percent,
            "cpu_count": cpu_count_logical,
            "memory_total": memory.total,
            "memory_used": memory.used,
            "memory_percent": memory.percent,
            "disk_total": disk.total,
            "disk_used": disk.used,
            "disk_percent": (disk.used / disk.total) * 100,
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Error collecting system metrics: {e}")
        return {
            "cpu_percent": 0,
            "memory_percent": 0,
            "disk_percent": 0,
            "timestamp": time.time()
        }

def execute_ollama_command(action: str):
    """Execute Ollama control commands."""
    try:
        if action == "start":
            # Start Ollama service
            result = subprocess.run(
                ["ollama", "serve"],
                capture_output=True,
                text=True,
                timeout=10,
                start_new_session=True
            )
            return {
                "success": True,
                "message": "Ollama service start command executed",
                "output": "Ollama service is starting in background..."
            }

        elif action == "stop":
            # Stop Ollama service
            result = subprocess.run(
                ["pkill", "-f", "ollama"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return {
                "success": True,
                "message": "Ollama service stopped",
                "output": "Ollama processes terminated"
            }

        elif action == "restart":
            # Restart Ollama service
            # First stop
            subprocess.run(["pkill", "-f", "ollama"], capture_output=True, timeout=5)
            time.sleep(2)
            # Then start
            subprocess.run(
                ["ollama", "serve"],
                capture_output=True,
                text=True,
                timeout=10,
                start_new_session=True
            )
            return {
                "success": True,
                "message": "Ollama service restarted",
                "output": "Ollama service stopped and restarted"
            }

        elif action == "status":
            # Check Ollama status
            try:
                import httpx
                with httpx.Client() as client:
                    response = client.get("http://localhost:11434/api/version", timeout=3.0)
                    if response.status_code == 200:
                        version_info = response.json()
                        return {
                            "success": True,
                            "message": "Ollama service is running",
                            "output": f"Version: {version_info.get('version', 'unknown')}"
                        }
                    else:
                        return {
                            "success": False,
                            "message": "Ollama service is not responding",
                            "output": f"HTTP {response.status_code}"
                        }
            except Exception as e:
                return {
                    "success": False,
                    "message": "Ollama service is not running",
                    "output": str(e)
                }
        else:
            return {
                "success": False,
                "message": f"Unknown action: {action}",
                "output": "Valid actions: start, stop, restart, status"
            }

    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "message": "Command timed out",
            "output": "The operation took too long to complete"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error executing command: {str(e)}",
            "output": ""
        }

def switch_inference_engine(target_engine: str):
    """Switch between vLLM and Ollama engines."""
    steps = []

    try:
        # Step 1: Stop all running engines
        steps.append("🛑 Stopping all inference engines...")

        # Stop Ollama
        try:
            subprocess.run(["pkill", "-f", "ollama"], capture_output=True, timeout=5)
            steps.append("   ✅ Ollama processes stopped")
        except:
            steps.append("   ⚠️ No Ollama processes to stop")

        # Stop vLLM (if any processes are running)
        try:
            subprocess.run(["pkill", "-f", "vllm"], capture_output=True, timeout=5)
            steps.append("   ✅ vLLM processes stopped")
        except:
            steps.append("   ⚠️ No vLLM processes to stop")

        # Wait for processes to fully stop
        time.sleep(3)
        steps.append("   ⏳ Waiting for GPU memory to be freed...")

        # Step 2: Start target engine
        if target_engine.lower() == "ollama":
            steps.append("🦙 Starting Ollama engine...")
            try:
                subprocess.run(
                    ["ollama", "serve"],
                    capture_output=True,
                    text=True,
                    timeout=10,
                    start_new_session=True
                )
                steps.append("   ✅ Ollama service started")

                # Wait and verify
                time.sleep(3)
                try:
                    import httpx
                    with httpx.Client() as client:
                        response = client.get("http://localhost:11434/api/version", timeout=5.0)
                        if response.status_code == 200:
                            steps.append("   ✅ Ollama service verified running")
                        else:
                            steps.append("   ⚠️ Ollama service may not be fully ready")
                except:
                    steps.append("   ⚠️ Could not verify Ollama status")

            except Exception as e:
                steps.append(f"   ❌ Failed to start Ollama: {str(e)}")
                return {
                    "success": False,
                    "message": f"Failed to start Ollama: {str(e)}",
                    "steps": steps
                }

        elif target_engine.lower() == "vllm":
            steps.append("🚀 vLLM engine ready for manual model loading...")
            steps.append("   💡 Load models through the web interface")
            steps.append("   💡 vLLM will start automatically when loading models")

        else:
            return {
                "success": False,
                "message": f"Unknown target engine: {target_engine}",
                "steps": steps
            }

        return {
            "success": True,
            "message": f"Successfully switched to {target_engine.upper()} engine",
            "steps": steps
        }

    except Exception as e:
        steps.append(f"❌ Error during engine switch: {str(e)}")
        return {
            "success": False,
            "message": f"Engine switch failed: {str(e)}",
            "steps": steps
        }

def execute_engine_control(action: str):
    """Execute general engine control commands."""
    try:
        if action == "stop_all":
            steps = []
            steps.append("🛑 Stopping all inference engines...")

            # Stop Ollama
            try:
                subprocess.run(["pkill", "-f", "ollama"], capture_output=True, timeout=5)
                steps.append("   ✅ Ollama processes stopped")
            except:
                steps.append("   ⚠️ No Ollama processes found")

            # Stop vLLM
            try:
                subprocess.run(["pkill", "-f", "vllm"], capture_output=True, timeout=5)
                steps.append("   ✅ vLLM processes stopped")
            except:
                steps.append("   ⚠️ No vLLM processes found")

            steps.append("   💾 GPU memory freed")

            return {
                "success": True,
                "message": "All inference engines stopped",
                "steps": steps
            }

        elif action == "status":
            steps = []

            # Check Ollama
            ollama_running = False
            try:
                import httpx
                with httpx.Client() as client:
                    response = client.get("http://localhost:11434/api/version", timeout=3.0)
                    if response.status_code == 200:
                        ollama_running = True
                        steps.append("🦙 Ollama: Running")
                    else:
                        steps.append("🦙 Ollama: Not responding")
            except:
                steps.append("🦙 Ollama: Stopped")

            # Check vLLM (simplified check)
            vllm_running = False
            try:
                result = subprocess.run(["pgrep", "-f", "vllm"], capture_output=True, timeout=3)
                if result.returncode == 0:
                    vllm_running = True
                    steps.append("🚀 vLLM: Running")
                else:
                    steps.append("🚀 vLLM: Stopped")
            except:
                steps.append("🚀 vLLM: Status unknown")

            # GPU memory status
            if ollama_running and vllm_running:
                steps.append("⚠️ WARNING: Both engines running - potential memory conflict!")
            elif ollama_running or vllm_running:
                steps.append("✅ Single engine running - optimal memory usage")
            else:
                steps.append("💾 No engines running - GPU memory available")

            return {
                "success": True,
                "message": "Engine status checked",
                "steps": steps
            }
        else:
            return {
                "success": False,
                "message": f"Unknown action: {action}",
                "output": "Valid actions: stop_all, status"
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error executing engine control: {str(e)}",
            "output": ""
        }

async def detect_engines():
    """Detect available inference engines."""
    global available_engines

    # Check vLLM
    try:
        import vllm
        available_engines["vllm"] = True
        logger.info("✅ vLLM engine detected")
    except ImportError:
        logger.info("⚠️  vLLM engine not available")

    # Check Ollama
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/version", timeout=2.0)
            if response.status_code == 200:
                available_engines["ollama"] = True
                logger.info("✅ Ollama engine detected")
            else:
                logger.info("⚠️  Ollama service not responding")
    except Exception:
        logger.info("⚠️  Ollama engine not available")

    logger.info(f"Available engines: {[k for k, v in available_engines.items() if v]}")

@app.on_event("startup")
async def startup_event():
    """Detect engines and initialize monitoring on startup."""
    initialize_gpu_monitoring()
    await detect_engines()

@app.get("/")
async def root():
    """Serve the main web interface."""
    try:
        with open("web/index.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <body>
                <h1>vLLM MAAS Platform (Multi-Engine)</h1>
                <p>Platform is running in multi-engine testing mode.</p>
                <p>Supported engines: vLLM, Ollama</p>
                <p>Visit <a href="/docs">/docs</a> for API documentation.</p>
                <p><a href="/test-gpu">GPU Metrics Test</a></p>
            </body>
        </html>
        """)

@app.get("/test-gpu")
async def test_gpu():
    """Serve GPU test page."""
    try:
        with open("test_gpu.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>GPU Test Page Not Found</h1>")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy", 
        "mode": "multi-engine-testing",
        "models_loaded": len(mock_models),
        "engines": available_engines
    }

@app.get("/engines")
async def get_engines():
    """Get available inference engines."""
    return available_engines

@app.get("/models", response_model=List[str])
async def list_models():
    """List all loaded models."""
    return list(mock_models.keys())

@app.get("/models/info", response_model=List[ModelInfo])
async def get_models_info():
    """Get detailed information about all loaded models."""
    models_info = []
    for name, info in mock_models.items():
        models_info.append(ModelInfo(
            name=name,
            status=info["status"],
            engine=info["engine"],
            gpu_memory_usage=0.0,
            total_requests=info.get("requests", 0),
            avg_latency=0.1
        ))
    return models_info

@app.get("/models/ollama/available")
async def list_ollama_models():
    """List available Ollama models."""
    if not available_engines["ollama"]:
        return {"models": []}
    
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags", timeout=5.0)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                return {"models": models}
    except Exception as e:
        logger.error(f"Error fetching Ollama models: {e}")
    
    # Return some common Ollama models as examples
    return {
        "models": [
            "llama2:7b",
            "codellama:7b", 
            "mistral:7b",
            "phi:2.7b",
            "neural-chat:7b"
        ]
    }

@app.post("/models/load")
async def load_model(request: ModelLoadRequest):
    """Mock model loading with engine selection."""
    engine = request.engine
    
    # Auto-detect engine if needed
    if engine == "auto":
        # Simple heuristics for engine selection
        if any(name in request.model_path.lower() for name in ['llama2', 'mistral', 'phi']):
            engine = "ollama" if available_engines["ollama"] else "vllm"
        else:
            engine = "vllm" if available_engines["vllm"] else "ollama"
    
    # Validate engine availability
    if engine not in available_engines or not available_engines[engine]:
        # Fall back to available engine or mock
        available = [k for k, v in available_engines.items() if v]
        if available:
            engine = available[0]
            logger.warning(f"Requested engine not available, using {engine}")
        else:
            engine = "mock"
    
    mock_models[request.model_name] = {
        "status": "loaded",
        "engine": engine,
        "path": request.model_path,
        "requests": 0
    }
    
    return {
        "message": f"Mock model {request.model_name} loaded successfully with {engine} engine",
        "engine": engine
    }

@app.delete("/models/{model_name}")
async def unload_model(model_name: str):
    """Mock model unloading."""
    if model_name in mock_models:
        del mock_models[model_name]
        return {"message": f"Mock model {model_name} unloaded successfully"}
    else:
        raise HTTPException(status_code=404, detail="Model not found")

@app.post("/generate", response_model=GenerateResponse)
async def generate_text(request: GenerateRequest):
    """Mock text generation with engine-specific responses."""
    if request.model_name not in mock_models:
        raise HTTPException(status_code=404, detail="Model not loaded")
    
    model_info = mock_models[request.model_name]
    engine = model_info["engine"]
    
    # Update request count
    model_info["requests"] += 1
    
    # Generate engine-specific mock response
    if engine == "ollama":
        if available_engines["ollama"]:
            # Try real Ollama generation
            try:
                import httpx
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        "http://localhost:11434/api/generate",
                        json={
                            "model": model_info["path"],
                            "prompt": request.prompt,
                            "stream": False,
                            "options": {
                                "temperature": request.temperature,
                                "top_p": request.top_p,
                                "num_predict": request.max_tokens
                            }
                        },
                        timeout=30.0
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        generated_text = data.get('response', '')
                        return GenerateResponse(
                            generated_text=generated_text,
                            tokens_generated=len(generated_text.split()),
                            latency=0.5,
                            engine="ollama"
                        )
            except Exception as e:
                logger.error(f"Ollama generation failed: {e}")
        
        # Fallback to mock
        mock_response = f"[Ollama Mock] You asked: '{request.prompt}'. This is a simulated Ollama response from {request.model_name}."
    
    elif engine == "vllm":
        mock_response = f"[vLLM Mock] Prompt: '{request.prompt}'. This is a simulated vLLM response from {request.model_name} with high performance."
    
    else:
        mock_response = f"[Mock Engine] You said: '{request.prompt}'. This is a simulated response from {request.model_name}."
    
    return GenerateResponse(
        generated_text=mock_response,
        tokens_generated=len(mock_response.split()),
        latency=0.1,
        engine=engine
    )

@app.post("/engine/switch")
async def switch_engine(request: EngineSwitchRequest):
    """Switch between inference engines (vLLM/Ollama)."""
    try:
        result = switch_inference_engine(request.target_engine)

        if result["success"]:
            # Update engine status after successful switch
            await asyncio.sleep(3)  # Wait for services to stabilize
            await detect_engines()

            return {
                "message": result["message"],
                "steps": result["steps"],
                "target_engine": request.target_engine
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Error switching engines: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to switch to {request.target_engine}: {str(e)}")

@app.post("/engine/control")
async def control_engines(request: EngineControlRequest):
    """Control inference engines (stop_all/status)."""
    try:
        result = execute_engine_control(request.action)

        if result["success"]:
            # Update engine status after successful command
            if request.action == "stop_all":
                await asyncio.sleep(2)  # Wait for services to stop
                await detect_engines()

            return {
                "message": result["message"],
                "steps": result.get("steps", []),
                "output": result.get("output", ""),
                "action": request.action
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Error controlling engines: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to {request.action}: {str(e)}")

@app.post("/ollama/control")
async def control_ollama(request: OllamaControlRequest):
    """Control Ollama service (start/stop/restart/status) - Legacy endpoint."""
    try:
        result = execute_ollama_command(request.action)

        if result["success"]:
            # Update engine status after successful command
            if request.action in ["start", "stop", "restart"]:
                await asyncio.sleep(2)  # Wait a bit for service to start/stop
                await detect_engines()

            return {
                "message": result["message"],
                "output": result["output"],
                "action": request.action
            }
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Error controlling Ollama: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to {request.action} Ollama: {str(e)}")

@app.get("/metrics")
async def get_metrics():
    """Get real system metrics with engine information."""
    # Get real system metrics
    system_metrics = get_system_metrics()
    system_metrics["mode"] = "multi-engine-testing"

    # Get real GPU metrics
    gpu_metrics = get_gpu_metrics()

    # Model metrics
    model_metrics = {
        name: {
            "requests": info.get("requests", 0),
            "latency": 0.1,
            "engine": info["engine"]
        }
        for name, info in mock_models.items()
    }

    return {
        "system": system_metrics,
        "gpu": gpu_metrics,
        "engines": available_engines,
        "models": model_metrics,
        "timestamp": time.time()
    }

if __name__ == "__main__":
    print("🚀 Starting vLLM MAAS Platform (Multi-Engine Mode)...")
    print("🌐 Web interface: http://localhost:8000")
    print("📊 API docs: http://localhost:8000/docs")
    print("🔧 Engines: vLLM + Ollama support")
    print("⚠️  Running in testing mode with mock/real responses")
    print("Press Ctrl+C to stop")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
