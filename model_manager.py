"""
Model Manager for vLLM MAAS Platform
Handles loading, unloading, and managing vLLM models.
"""

import asyncio
import logging
import os
from typing import Dict, Any, Optional, List
import gc
import torch

from vllm import LLM, SamplingParams
from vllm.engine.arg_utils import AsyncEngineArgs
from vllm.engine.async_llm_engine import AsyncLLMEngine

logger = logging.getLogger(__name__)


class ModelManager:
    """Manages vLLM model instances and their lifecycle."""
    
    def __init__(self, settings):
        self.settings = settings
        self.loaded_models: Dict[str, Dict[str, Any]] = {}
        self.model_engines: Dict[str, AsyncLLMEngine] = {}
        self._lock = asyncio.Lock()
    
    async def load_model(
        self,
        model_name: str,
        model_path: str,
        gpu_memory_utilization: float = 0.9,
        max_model_len: Optional[int] = None,
        tensor_parallel_size: int = 1
    ) -> bool:
        """Load a model using vLLM."""
        async with self._lock:
            if model_name in self.loaded_models:
                logger.warning(f"Model {model_name} is already loaded")
                return False
            
            try:
                logger.info(f"Loading model {model_name} from {model_path}")
                
                # Set model status to loading
                self.loaded_models[model_name] = {
                    "status": "loading",
                    "model_path": model_path,
                    "gpu_memory_utilization": gpu_memory_utilization,
                    "max_model_len": max_model_len,
                    "tensor_parallel_size": tensor_parallel_size
                }
                
                # Configure engine arguments
                engine_args = AsyncEngineArgs(
                    model=model_path,
                    gpu_memory_utilization=gpu_memory_utilization,
                    max_model_len=max_model_len,
                    tensor_parallel_size=tensor_parallel_size,
                    trust_remote_code=True,
                    disable_log_stats=False,
                )
                
                # Create async engine
                engine = AsyncLLMEngine.from_engine_args(engine_args)
                self.model_engines[model_name] = engine
                
                # Update status to loaded
                self.loaded_models[model_name]["status"] = "loaded"
                self.loaded_models[model_name]["engine"] = engine
                
                logger.info(f"Successfully loaded model {model_name}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to load model {model_name}: {e}")
                # Clean up on failure
                if model_name in self.loaded_models:
                    del self.loaded_models[model_name]
                if model_name in self.model_engines:
                    del self.model_engines[model_name]
                raise e
    
    async def unload_model(self, model_name: str) -> bool:
        """Unload a model and free GPU memory."""
        async with self._lock:
            if model_name not in self.loaded_models:
                logger.warning(f"Model {model_name} is not loaded")
                return False
            
            try:
                logger.info(f"Unloading model {model_name}")
                
                # Update status
                self.loaded_models[model_name]["status"] = "unloading"
                
                # Stop the engine
                if model_name in self.model_engines:
                    engine = self.model_engines[model_name]
                    # Note: vLLM doesn't have explicit cleanup method
                    # We rely on garbage collection
                    del self.model_engines[model_name]
                
                # Remove from loaded models
                del self.loaded_models[model_name]
                
                # Force garbage collection to free GPU memory
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                logger.info(f"Successfully unloaded model {model_name}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to unload model {model_name}: {e}")
                raise e
    
    async def generate(
        self,
        model_name: str,
        prompt: str,
        max_tokens: int = 2048,
        temperature: float = 0.7,
        top_p: float = 0.9,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Generate text using a loaded model."""
        if model_name not in self.loaded_models:
            raise ValueError(f"Model {model_name} is not loaded")
        
        if self.loaded_models[model_name]["status"] != "loaded":
            raise ValueError(f"Model {model_name} is not ready (status: {self.loaded_models[model_name]['status']})")
        
        try:
            engine = self.model_engines[model_name]
            
            # Create sampling parameters
            sampling_params = SamplingParams(
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens,
            )
            
            # Generate text
            request_id = f"{model_name}_{asyncio.get_event_loop().time()}"
            
            if stream:
                # Streaming generation
                results = []
                async for request_output in engine.generate(prompt, sampling_params, request_id):
                    if request_output.finished:
                        output = request_output.outputs[0]
                        results.append({
                            "text": output.text,
                            "tokens_generated": len(output.token_ids),
                            "finish_reason": output.finish_reason
                        })
                return results[-1] if results else {"text": "", "tokens_generated": 0}
            else:
                # Non-streaming generation
                final_output = None
                async for request_output in engine.generate(prompt, sampling_params, request_id):
                    if request_output.finished:
                        output = request_output.outputs[0]
                        final_output = {
                            "text": output.text,
                            "tokens_generated": len(output.token_ids),
                            "finish_reason": output.finish_reason
                        }
                        break
                
                return final_output or {"text": "", "tokens_generated": 0}
                
        except Exception as e:
            logger.error(f"Error generating text with model {model_name}: {e}")
            raise e
    
    async def get_model_status(self, model_name: str) -> Optional[str]:
        """Get the status of a specific model."""
        return self.loaded_models.get(model_name, {}).get("status")
    
    async def list_loaded_models(self) -> List[str]:
        """List all loaded model names."""
        return [name for name, info in self.loaded_models.items() if info["status"] == "loaded"]
    
    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a model."""
        return self.loaded_models.get(model_name)
    
    async def shutdown(self):
        """Shutdown all models and clean up resources."""
        logger.info("Shutting down model manager...")
        
        # Unload all models
        model_names = list(self.loaded_models.keys())
        for model_name in model_names:
            try:
                await self.unload_model(model_name)
            except Exception as e:
                logger.error(f"Error unloading model {model_name} during shutdown: {e}")
        
        # Final cleanup
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("Model manager shutdown complete")
