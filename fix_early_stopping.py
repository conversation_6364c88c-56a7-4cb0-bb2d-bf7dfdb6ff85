#!/usr/bin/env python3
"""
<PERSON>ript to fix early stopping issues in text generation.
This script modifies the generation parameters to prevent premature termination.
"""

import json
import os
import shutil
from pathlib import Path

def backup_and_modify_generation_config():
    """Backup and modify the generation config to prevent early stopping."""
    
    config_path = Path("models/qwen3-8b/generation_config.json")
    backup_path = Path("models/qwen3-8b/generation_config.json.backup")
    
    if not config_path.exists():
        print(f"❌ Generation config not found at {config_path}")
        return False
    
    try:
        # Create backup
        if not backup_path.exists():
            shutil.copy2(config_path, backup_path)
            print(f"✅ Created backup at {backup_path}")
        
        # Read current config
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("Current generation config:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        # Modify config to prevent early stopping
        modified = False
        
        # Remove BOS token from EOS tokens if present
        if "eos_token_id" in config:
            if isinstance(config["eos_token_id"], list):
                original_eos = config["eos_token_id"].copy()
                # Remove BOS token (151643) from EOS tokens if present
                if 151643 in config["eos_token_id"]:
                    config["eos_token_id"] = [token for token in config["eos_token_id"] if token != 151643]
                    print(f"✅ Removed BOS token (151643) from EOS tokens")
                    print(f"   Original EOS tokens: {original_eos}")
                    print(f"   New EOS tokens: {config['eos_token_id']}")
                    modified = True
        
        # Ensure we have reasonable generation parameters
        if "max_new_tokens" not in config:
            config["max_new_tokens"] = 2048
            print("✅ Added max_new_tokens: 2048")
            modified = True
        
        # Adjust temperature and sampling parameters for better generation
        if config.get("temperature", 0) < 0.1:
            config["temperature"] = 0.7
            print("✅ Increased temperature to 0.7 for better generation")
            modified = True
        
        # Ensure proper sampling
        if not config.get("do_sample", True):
            config["do_sample"] = True
            print("✅ Enabled sampling")
            modified = True
        
        # Add repetition penalty to prevent loops
        if "repetition_penalty" not in config:
            config["repetition_penalty"] = 1.1
            print("✅ Added repetition_penalty: 1.1")
            modified = True
        
        if modified:
            # Write modified config
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Updated generation config saved to {config_path}")
            print("\nNew generation config:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
            return True
        else:
            print("ℹ️  No modifications needed")
            return True
            
    except Exception as e:
        print(f"❌ Error modifying generation config: {e}")
        return False

def restore_backup():
    """Restore the original generation config from backup."""
    config_path = Path("models/qwen3-8b/generation_config.json")
    backup_path = Path("models/qwen3-8b/generation_config.json.backup")
    
    if backup_path.exists():
        shutil.copy2(backup_path, config_path)
        print(f"✅ Restored original config from {backup_path}")
        return True
    else:
        print(f"❌ No backup found at {backup_path}")
        return False

def main():
    """Main function to fix early stopping issues."""
    print("Text Generation Early Stopping Fix")
    print("=" * 50)
    
    print("\nThis script will:")
    print("1. Backup your current generation config")
    print("2. Modify parameters to prevent early stopping")
    print("3. Remove BOS token from EOS tokens if present")
    print("4. Add better generation parameters")
    
    choice = input("\nProceed with the fix? (y/n): ").lower().strip()
    
    if choice == 'y':
        if backup_and_modify_generation_config():
            print("\n✅ Fix applied successfully!")
            print("\nNext steps:")
            print("1. Restart your model server")
            print("2. Test generation with: python test_generation_fix.py")
            print("3. If issues persist, you can restore with: python fix_early_stopping.py --restore")
        else:
            print("\n❌ Fix failed!")
    elif choice == 'restore' or '--restore' in str(choice):
        restore_backup()
    else:
        print("Operation cancelled.")

if __name__ == "__main__":
    import sys
    if "--restore" in sys.argv:
        restore_backup()
    else:
        main()
