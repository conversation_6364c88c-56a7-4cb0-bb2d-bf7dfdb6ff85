#!/usr/bin/env python3
"""
测试 Unsloth 服务是否正常工作
"""

import requests
import json
import time

def test_health():
    """测试健康状态"""
    print("🔍 测试服务健康状态...")
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print("✅ 服务健康状态正常")
            print(f"   引擎: {health.get('engine')}")
            print(f"   Unsloth 可用: {health.get('unsloth_available')}")
            print(f"   已加载模型: {health.get('models_loaded')}")
            print(f"   GPU 数量: {health.get('gpu_count')}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🚀 测试模型加载...")
    
    load_request = {
        "model_name": "qwen3-8b",
        "max_seq_length": 2048,
        "load_in_4bit": True,
        "gpu_device": None,  # 自动选择
        "device_map": "auto"  # 自动分布到多GPU
    }
    
    try:
        print(f"发送加载请求: {load_request}")
        response = requests.post(
            "http://localhost:8000/models/quick-load",
            json=load_request,
            timeout=120  # 2分钟超时
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 模型加载成功!")
            print(f"   响应: {result}")
            return True
        else:
            print(f"❌ 模型加载失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 模型加载超时")
        return False
    except Exception as e:
        print(f"❌ 模型加载异常: {e}")
        return False

def test_text_generation():
    """测试文本生成"""
    print("\n📝 测试文本生成...")
    
    # 使用您之前的中文测试文本
    test_prompt = "太阳和月亮哪个更大？这个问题可能有两种理解方式："
    
    generate_request = {
        "model_name": "qwen3-8b",
        "prompt": test_prompt,
        "max_new_tokens": 512,  # 使用 max_new_tokens 而不是 max_tokens
        "temperature": 0.7,
        "top_p": 0.9,
        "do_sample": True
    }
    
    try:
        print(f"测试提示: {test_prompt}")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8000/generate",
            json=generate_request,
            timeout=60
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文本生成成功!")
            print(f"   延迟: {result['latency']:.2f}s")
            print(f"   生成 token 数: {result['tokens_generated']}")
            print(f"   引擎: {result['engine']}")
            print(f"   生成文本: {result['generated_text']}")
            
            # 检查是否解决了截断问题
            if result['tokens_generated'] > 10:
                print("🎉 截断问题已解决！生成了足够的文本")
            else:
                print("⚠️ 仍然存在截断问题")
            
            return True
        else:
            print(f"❌ 文本生成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 文本生成异常: {e}")
        return False

def test_gpu_info():
    """测试 GPU 信息"""
    print("\n💾 测试 GPU 信息...")
    
    try:
        response = requests.get("http://localhost:8000/gpu/info", timeout=10)
        if response.status_code == 200:
            gpu_info = response.json()
            print("✅ GPU 信息获取成功:")
            
            for gpu in gpu_info.get('gpus', []):
                print(f"   GPU {gpu['id']}: {gpu['name']}")
                print(f"   显存: {gpu['memory_total']:.1f}GB")
                print(f"   已用: {gpu['memory_used']:.1f}GB")
                print(f"   使用率: {gpu['utilization']:.1f}%")
                
            return True
        else:
            print(f"❌ 获取 GPU 信息失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取 GPU 信息异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Unsloth 服务功能测试")
    print("=" * 50)
    
    # 1. 测试健康状态
    if not test_health():
        print("\n❌ 服务未正常运行，请检查")
        return
    
    # 2. 测试 GPU 信息
    test_gpu_info()
    
    # 3. 测试模型加载
    if test_model_loading():
        # 4. 测试文本生成
        test_text_generation()
    else:
        print("\n💡 模型加载失败的可能原因:")
        print("1. 模型文件不存在于 ./models/qwen3-8b/")
        print("2. 显存不足")
        print("3. Unsloth 配置问题")
    
    print("\n" + "=" * 50)
    print("✅ 测试完成!")
    print("\n🌐 您可以访问以下地址:")
    print("   Web 界面: http://localhost:8000")
    print("   API 文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
