#!/bin/bash
# CUDA 升级前的环境备份脚本

echo "🔄 创建环境备份..."

# 1. 备份 pip 包列表
pip freeze > requirements_backup_$(date +%Y%m%d_%H%M%S).txt
echo "✅ 已备份 pip 包列表"

# 2. 备份虚拟环境（如果空间足够）
if [ -d "venv" ]; then
    echo "📁 检测到虚拟环境，创建备份..."
    cp -r venv venv_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "⚠️ 虚拟环境太大，跳过完整备份"
fi

# 3. 测试当前环境
python -c "
import torch
print(f'备份前环境测试:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
" > environment_backup_$(date +%Y%m%d_%H%M%S).log

echo "✅ 备份完成"
