#!/usr/bin/env python3
"""
vLLM 配置优化脚本 - 针对 2×RTX 2080 Ti (22GB) 运行 Qwen3-8B
"""

import os
import torch
from vllm import LLM, SamplingParams
from vllm.engine.arg_utils import AsyncEngineArgs
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_optimal_vllm_config():
    """获取针对 2×2080Ti 的最优 vLLM 配置"""
    
    # 检查 GPU 配置
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA 不可用")
    
    gpu_count = torch.cuda.device_count()
    logger.info(f"检测到 {gpu_count} 个 GPU")
    
    for i in range(gpu_count):
        gpu_props = torch.cuda.get_device_properties(i)
        memory_gb = gpu_props.total_memory / (1024**3)
        logger.info(f"GPU {i}: {gpu_props.name}, {memory_gb:.1f}GB 显存")
    
    # 针对 2080Ti 的优化配置
    config = {
        # 基础配置
        "model": "./models/qwen3-8b",
        "tensor_parallel_size": min(2, gpu_count),  # 使用双卡并行
        "gpu_memory_utilization": 0.85,  # 使用85%显存，留出缓冲
        
        # 序列长度优化
        "max_model_len": 8192,  # 限制序列长度以节省显存
        "block_size": 16,  # 优化内存块大小
        
        # 性能优化
        "swap_space": 4,  # 4GB 交换空间，防止OOM
        "disable_log_stats": False,  # 保留统计信息
        "enforce_eager": False,  # 使用 CUDA Graph 优化
        
        # 数据类型优化
        "dtype": "bfloat16",  # 使用 bfloat16 减少显存
        
        # 批处理优化
        "max_num_seqs": 64,  # 最大并发序列数
        "max_num_batched_tokens": 8192,  # 最大批处理 token 数
        
        # 其他优化
        "trust_remote_code": True,
        "enable_prefix_caching": True,  # 启用前缀缓存
    }
    
    return config

def create_optimized_llm():
    """创建优化的 vLLM 实例"""
    config = get_optimal_vllm_config()
    
    logger.info("正在创建优化的 vLLM 实例...")
    logger.info(f"配置: {config}")
    
    try:
        llm = LLM(**config)
        logger.info("✅ vLLM 实例创建成功!")
        return llm
    except Exception as e:
        logger.error(f"❌ vLLM 实例创建失败: {e}")
        
        # 降级配置
        logger.info("尝试降级配置...")
        fallback_config = config.copy()
        fallback_config.update({
            "gpu_memory_utilization": 0.75,  # 降低到75%
            "max_model_len": 4096,  # 进一步限制序列长度
            "max_num_seqs": 32,  # 减少并发数
            "swap_space": 8,  # 增加交换空间
        })
        
        try:
            llm = LLM(**fallback_config)
            logger.info("✅ 使用降级配置创建成功!")
            return llm
        except Exception as e2:
            logger.error(f"❌ 降级配置也失败: {e2}")
            raise e2

def test_inference(llm):
    """测试推理性能"""
    test_prompts = [
        "太阳和月亮哪个更大？",
        "请解释人工智能的发展历程。",
        "写一首关于春天的诗。"
    ]
    
    # 采样参数
    sampling_params = SamplingParams(
        temperature=0.7,
        top_p=0.9,
        max_tokens=512,  # 适中的生成长度
    )
    
    logger.info("开始推理测试...")
    
    for i, prompt in enumerate(test_prompts, 1):
        logger.info(f"测试 {i}: {prompt}")
        
        try:
            import time
            start_time = time.time()
            
            outputs = llm.generate([prompt], sampling_params)
            
            end_time = time.time()
            latency = end_time - start_time
            
            generated_text = outputs[0].outputs[0].text
            tokens_generated = len(outputs[0].outputs[0].token_ids)
            
            logger.info(f"✅ 生成成功!")
            logger.info(f"   延迟: {latency:.2f}s")
            logger.info(f"   生成 token 数: {tokens_generated}")
            logger.info(f"   生成文本: {generated_text[:100]}...")
            
        except Exception as e:
            logger.error(f"❌ 推理失败: {e}")

def check_memory_usage():
    """检查显存使用情况"""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / (1024**3)
            reserved = torch.cuda.memory_reserved(i) / (1024**3)
            total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            
            logger.info(f"GPU {i} 显存使用:")
            logger.info(f"  已分配: {allocated:.2f}GB")
            logger.info(f"  已保留: {reserved:.2f}GB") 
            logger.info(f"  总显存: {total:.2f}GB")
            logger.info(f"  使用率: {(reserved/total)*100:.1f}%")

if __name__ == "__main__":
    print("🚀 vLLM 2080Ti 优化配置测试")
    print("=" * 50)
    
    try:
        # 创建优化的 LLM 实例
        llm = create_optimized_llm()
        
        # 检查显存使用
        check_memory_usage()
        
        # 测试推理
        test_inference(llm)
        
        # 最终显存检查
        print("\n" + "=" * 50)
        print("最终显存使用情况:")
        check_memory_usage()
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print("\n💡 建议:")
        print("1. 确保模型文件存在于 ./models/qwen3-8b/")
        print("2. 尝试使用量化版本的模型")
        print("3. 考虑减少 max_model_len 参数")
        print("4. 检查 CUDA 和 vLLM 安装是否正确")
