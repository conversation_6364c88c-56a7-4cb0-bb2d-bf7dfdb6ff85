#!/usr/bin/env python3
"""
CUDA 兼容性检查和修复脚本
解决 vLLM 与 CUDA 11.8 的兼容性问题
"""

import os
import sys
import subprocess
import importlib.util

def check_cuda_environment():
    """检查 CUDA 环境"""
    print("🔍 检查 CUDA 环境...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA 可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ CUDA 版本: {torch.version.cuda}")
            print(f"✅ GPU 数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                memory_gb = props.total_memory / (1024**3)
                print(f"   GPU {i}: {props.name} ({memory_gb:.1f}GB)")
        
        return torch.version.cuda
    except ImportError:
        print("❌ PyTorch 未安装")
        return None

def check_vllm_compatibility():
    """检查 vLLM 兼容性"""
    print("\n🔍 检查 vLLM 兼容性...")
    
    try:
        import vllm
        print(f"✅ vLLM 已安装: {vllm.__version__}")
        
        # 尝试导入核心组件
        from vllm import LLM, SamplingParams
        print("✅ vLLM 核心组件导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ vLLM 导入失败: {e}")
        if "libcudart.so.12" in str(e):
            print("   原因: CUDA 12.x 库缺失，但系统使用 CUDA 11.8")
        return False
    except Exception as e:
        print(f"❌ vLLM 其他错误: {e}")
        return False

def install_compatible_vllm(cuda_version):
    """安装兼容的 vLLM 版本"""
    print(f"\n🔧 为 CUDA {cuda_version} 安装兼容的 vLLM...")
    
    if cuda_version.startswith("11.8"):
        # CUDA 11.8 兼容版本
        compatible_versions = [
            "vllm==0.6.3.post1",  # 支持 CUDA 11.8
            "vllm==0.5.5",        # 备选版本
            "vllm==0.4.3"         # 更早的稳定版本
        ]
    else:
        print(f"⚠️ 未知的 CUDA 版本: {cuda_version}")
        return False
    
    for version in compatible_versions:
        print(f"尝试安装 {version}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", version, "--force-reinstall"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {version} 安装成功")
                return True
            else:
                print(f"❌ {version} 安装失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"❌ {version} 安装超时")
        except Exception as e:
            print(f"❌ {version} 安装异常: {e}")
    
    return False

def create_fallback_config():
    """创建降级配置"""
    print("\n🔄 创建降级配置...")
    
    fallback_code = '''
# 降级配置：使用 Unsloth 替代 vLLM
import os
import logging

logger = logging.getLogger(__name__)

def use_unsloth_fallback():
    """使用 Unsloth 作为 vLLM 的降级替代"""
    logger.info("🔄 vLLM 不可用，切换到 Unsloth 模式")
    
    # 设置环境变量指示使用 Unsloth
    os.environ["USE_UNSLOTH_FALLBACK"] = "1"
    
    print("💡 建议:")
    print("1. 使用 app_unsloth.py 替代 app_vllm_real.py")
    print("2. 或者使用 app_multi_engine.py 的多引擎模式")
    print("3. Unsloth 在您的硬件上可能更稳定")
    
    return True

# 自动检测并切换
if __name__ == "__main__":
    use_unsloth_fallback()
'''
    
    with open("cuda_fallback.py", "w", encoding="utf-8") as f:
        f.write(fallback_code)
    
    print("✅ 降级配置已创建: cuda_fallback.py")

def fix_fastapi_deprecation():
    """修复 FastAPI 弃用警告"""
    print("\n🔧 修复 FastAPI 弃用警告...")
    
    # 这个已经在之前的代码中处理了
    print("✅ FastAPI 弃用警告已在代码中修复")

def recommend_solution():
    """推荐解决方案"""
    print("\n💡 推荐解决方案:")
    print("=" * 50)
    
    print("由于您的系统使用 CUDA 11.8，而最新的 vLLM 需要 CUDA 12.x，")
    print("建议使用以下方案之一:")
    print()
    
    print("🥇 方案1: 使用 Unsloth (推荐)")
    print("   - 专为您的硬件优化")
    print("   - 支持 CUDA 11.8")
    print("   - 启动命令: python app_unsloth.py")
    print()
    
    print("🥈 方案2: 使用多引擎模式")
    print("   - 同时支持多种推理引擎")
    print("   - 自动降级到可用引擎")
    print("   - 启动命令: python app.py")
    print()
    
    print("🥉 方案3: 升级 CUDA (高级用户)")
    print("   - 安装 CUDA 12.x Toolkit")
    print("   - 重新安装 PyTorch (CUDA 12.x 版本)")
    print("   - 风险: 可能影响其他项目")
    print()
    
    print("🚀 立即尝试:")
    print("   python app_unsloth.py")

def main():
    """主函数"""
    print("🔧 CUDA 兼容性检查和修复工具")
    print("=" * 50)
    
    # 1. 检查 CUDA 环境
    cuda_version = check_cuda_environment()
    if not cuda_version:
        print("❌ CUDA 环境检查失败")
        return
    
    # 2. 检查 vLLM 兼容性
    vllm_ok = check_vllm_compatibility()
    
    if not vllm_ok:
        print(f"\n⚠️ vLLM 与 CUDA {cuda_version} 不兼容")
        
        # 3. 尝试安装兼容版本
        if not install_compatible_vllm(cuda_version):
            print("❌ 无法安装兼容的 vLLM 版本")
            
            # 4. 创建降级配置
            create_fallback_config()
    
    # 5. 修复其他问题
    fix_fastapi_deprecation()
    
    # 6. 推荐解决方案
    recommend_solution()
    
    print("\n" + "=" * 50)
    print("✅ 检查完成!")

if __name__ == "__main__":
    main()
