#!/usr/bin/env python3
"""
vLLM MAAS (Model as a Service) Management Platform
A comprehensive platform for managing and serving large language models using vLLM.
"""

import asyncio
import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from inference_manager import InferenceManager, InferenceEngine
from config import Settings
from monitoring import MetricsCollector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances
inference_manager: Optional[InferenceManager] = None
metrics_collector: Optional[MetricsCollector] = None
settings = Settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global inference_manager, metrics_collector

    # Startup
    logger.info("Starting vLLM MAAS Platform with Multi-Engine Support...")
    inference_manager = InferenceManager(settings)
    await inference_manager.initialize()

    metrics_collector = MetricsCollector()

    # Start metrics collection
    asyncio.create_task(metrics_collector.start_collection())

    yield

    # Shutdown
    logger.info("Shutting down vLLM MAAS Platform...")
    if inference_manager:
        await inference_manager.shutdown()
    if metrics_collector:
        await metrics_collector.stop_collection()


# Create FastAPI app
app = FastAPI(
    title="vLLM MAAS Platform",
    description="Multi-Engine Model as a Service platform (vLLM + Ollama)",
    version="1.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class ModelLoadRequest(BaseModel):
    model_name: str
    model_path: str
    engine: str = "auto"  # "vllm", "ollama", or "auto"
    gpu_memory_utilization: float = 0.9
    max_model_len: Optional[int] = None
    tensor_parallel_size: int = 1

class GenerateRequest(BaseModel):
    model_name: str
    prompt: str
    max_tokens: int = 2048
    temperature: float = 0.7
    top_p: float = 0.9
    stream: bool = False

class ModelInfo(BaseModel):
    name: str
    status: str
    engine: str
    gpu_memory_usage: float = 0.0
    total_requests: int = 0
    avg_latency: float = 0.0

class GenerateResponse(BaseModel):
    generated_text: str
    tokens_generated: int
    latency: float
    engine: str


# API Routes
@app.get("/")
async def root():
    """Serve the main web interface."""
    with open("web/index.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    engines = await inference_manager.get_available_engines()
    return {
        "status": "healthy",
        "models_loaded": len(inference_manager.loaded_models),
        "engines": engines
    }

@app.get("/models", response_model=List[str])
async def list_models():
    """List all loaded models."""
    return await inference_manager.list_loaded_models()

@app.get("/models/info", response_model=List[ModelInfo])
async def get_models_info():
    """Get detailed information about all loaded models."""
    models_info = []
    detailed_info = await inference_manager.get_models_info()

    for model_info in detailed_info:
        metrics = metrics_collector.get_model_metrics(model_info["name"])
        models_info.append(ModelInfo(
            name=model_info["name"],
            status=model_info["status"],
            engine=model_info["engine"],
            gpu_memory_usage=metrics.get("gpu_memory_usage", 0.0),
            total_requests=metrics.get("total_requests", 0),
            avg_latency=metrics.get("avg_latency", 0.0)
        ))
    return models_info

@app.post("/models/load")
async def load_model(request: ModelLoadRequest, background_tasks: BackgroundTasks):
    """Load a new model with specified engine."""
    try:
        background_tasks.add_task(
            inference_manager.load_model,
            request.model_name,
            request.model_path,
            request.engine,
            gpu_memory_utilization=request.gpu_memory_utilization,
            max_model_len=request.max_model_len,
            tensor_parallel_size=request.tensor_parallel_size
        )
        return {
            "message": f"Loading model {request.model_name} with {request.engine} engine...",
            "engine": request.engine
        }
    except Exception as e:
        logger.error(f"Error loading model {request.model_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/models/{model_name}")
async def unload_model(model_name: str):
    """Unload a model."""
    try:
        success = await inference_manager.unload_model(model_name)
        if success:
            return {"message": f"Model {model_name} unloaded successfully"}
        else:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
    except Exception as e:
        logger.error(f"Error unloading model {model_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/generate", response_model=GenerateResponse)
async def generate_text(request: GenerateRequest):
    """Generate text using a loaded model."""
    try:
        start_time = asyncio.get_event_loop().time()

        result = await inference_manager.generate(
            model_name=request.model_name,
            prompt=request.prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stream=request.stream
        )

        end_time = asyncio.get_event_loop().time()
        latency = end_time - start_time

        # Update metrics
        metrics_collector.record_request(request.model_name, latency, result.get("tokens_generated", 0))

        return GenerateResponse(
            generated_text=result["text"],
            tokens_generated=result["tokens_generated"],
            latency=latency,
            engine=result.get("engine", "unknown")
        )
    except Exception as e:
        logger.error(f"Error generating text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/engines")
async def get_engines():
    """Get available inference engines."""
    return await inference_manager.get_available_engines()

@app.get("/models/ollama/available")
async def list_ollama_models():
    """List available Ollama models."""
    try:
        models = await inference_manager.list_ollama_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Error listing Ollama models: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics")
async def get_metrics():
    """Get system and model metrics."""
    return metrics_collector.get_all_metrics()


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
