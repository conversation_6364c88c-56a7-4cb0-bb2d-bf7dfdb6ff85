#!/bin/bash
# CUDA 回滚脚本

set -e

echo "🔄 开始回滚到 CUDA 11.8..."

# 1. 卸载 CUDA 12.x 版本
echo "🗑️ 卸载 CUDA 12.x 包..."
pip uninstall -y torch torchvision torchaudio

# 2. 重新安装 CUDA 11.8 版本
echo "📦 重新安装 CUDA 11.8 版本..."
pip install torch==2.7.1+cu118 torchvision==0.22.1+cu118 torchaudio==2.7.1+cu118 --index-url https://download.pytorch.org/whl/cu118

# 3. 验证回滚
echo "✅ 验证回滚..."
python -c "
import torch
print(f'回滚后环境:')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.version.cuda}')
print(f'GPU 可用: {torch.cuda.is_available()}')
"

echo "✅ 回滚完成！"
