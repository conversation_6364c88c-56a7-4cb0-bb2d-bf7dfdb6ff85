torchvision-0.21.0+cu124.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.21.0+cu124.dist-info/LICENSE,sha256=ZQL2doUc_iX4r3VTHfsyN1tzJbc8N-e0N0H6QiiT5x0,1517
torchvision-0.21.0+cu124.dist-info/METADATA,sha256=Olffhk06P_R7360LiiMj0dghW_dnbz1pOzec11kXnOE,6140
torchvision-0.21.0+cu124.dist-info/RECORD,,
torchvision-0.21.0+cu124.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.21.0+cu124.dist-info/WHEEL,sha256=Fa4luOZMtbP9T1lH9r5CAb1tiEKk2EoLvCD0sxktAMY,104
torchvision-0.21.0+cu124.dist-info/top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision.libs/libcudart.41118559.so.12,sha256=h3QiT1sRpzsV0HSj_M5zJzIsXEz9_ZJNaoJnee7JaP4,707904
torchvision.libs/libjpeg.1c1c4b09.so.8,sha256=4g0_-nHW9O7mxv9bXLzFpOSE-qh6R-p3uvlk46XfJp8,664504
torchvision.libs/libnvjpeg.02b6d700.so.12,sha256=btLqpaLN_zk7yVxUNHS5YRemcYi76kbqdNEvf4OHR5c,6722352
torchvision.libs/libpng16.0364a1db.so.16,sha256=XHT13jO77EQJX8hxsCRwNo7ZG3J-g1CRsyCuZhAvxns,279617
torchvision.libs/libsharpyuv.5c41a003.so.0,sha256=-eg4IlWNkqxcjPdLHw4P5KRbvCRIqde5D6KwU1VOCbw,33632
torchvision.libs/libwebp.54a0d02a.so.7,sha256=G2OQ2SplenNV4UEOvCUpJ08szkGXY15doXitLl4Ej8U,712281
torchvision.libs/libz.d13a2644.so.1,sha256=Cw5oKp3H_UiVpngyiPhRt5PciWM_KHFAJ5dPpNZvORQ,124744
torchvision/_C.so,sha256=G8EaPzd7lXK6r5LForyBeCQowFqGWguBetPORHY_vj4,7761160
torchvision/__init__.py,sha256=7iyfQRDPEgPbSMQmAWBzKawfGXCfqRwVL42V61NDenM,3534
torchvision/__pycache__/__init__.cpython-312.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-312.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-312.pyc,,
torchvision/__pycache__/_utils.cpython-312.pyc,,
torchvision/__pycache__/extension.cpython-312.pyc,,
torchvision/__pycache__/utils.cpython-312.pyc,,
torchvision/__pycache__/version.cpython-312.pyc,,
torchvision/_internally_replaced_utils.py,sha256=SmFV-P4ETuJBIbrFUV4s1rXSpP_FI7U72CPO6q-6mwA,1407
torchvision/_meta_registrations.py,sha256=lkEGW61fKUrGSh0iOFsZ1ZHskItS1EJ9Oo2UfM-OvQ8,7208
torchvision/_utils.py,sha256=6TWK0JGaZVQrofgCAp5ox61_NQE2gIwhYouKQMiTaJ8,934
torchvision/datasets/__init__.py,sha256=CQfKDHOvjSZ2dBlOLRieEjP9AsB1jJEe3-5vbFNeXOM,3606
torchvision/datasets/__pycache__/__init__.cpython-312.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-312.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-312.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-312.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-312.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-312.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-312.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-312.pyc,,
torchvision/datasets/__pycache__/coco.cpython-312.pyc,,
torchvision/datasets/__pycache__/country211.cpython-312.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-312.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-312.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-312.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-312.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-312.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-312.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-312.pyc,,
torchvision/datasets/__pycache__/folder.cpython-312.pyc,,
torchvision/datasets/__pycache__/food101.cpython-312.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-312.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-312.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-312.pyc,,
torchvision/datasets/__pycache__/imagenette.cpython-312.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-312.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-312.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-312.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-312.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-312.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-312.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-312.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-312.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-312.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-312.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-312.pyc,,
torchvision/datasets/__pycache__/places365.cpython-312.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-312.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-312.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-312.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-312.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-312.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-312.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-312.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-312.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-312.pyc,,
torchvision/datasets/__pycache__/usps.cpython-312.pyc,,
torchvision/datasets/__pycache__/utils.cpython-312.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-312.pyc,,
torchvision/datasets/__pycache__/vision.cpython-312.pyc,,
torchvision/datasets/__pycache__/voc.cpython-312.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-312.pyc,,
torchvision/datasets/_optical_flow.py,sha256=oRm_6rlBpJyi9d2IeTiebHssDEXQDKEKGw3ZqNVDMrg,19697
torchvision/datasets/_stereo_matching.py,sha256=HzSLkWTXj26lpC9smajleUIgt_rsKEoull9NGW9jPE0,49058
torchvision/datasets/caltech.py,sha256=IRpfSK1K-26f8TTQUojA9wWDHhXbPZRqZBIjJ_WhE60,8815
torchvision/datasets/celeba.py,sha256=k-2W5IfCKpVTWb18LAHShnr_rwsMQ0CdqJ3REKSWJrk,8437
torchvision/datasets/cifar.py,sha256=TqgqwpAMnr2LQ2iu6NdhkmYy8ChJ5YMpL2N_zTmqvdU,5791
torchvision/datasets/cityscapes.py,sha256=a_FbxaDP2PRXM6TF44rK_vyCbziX7AxcZOYGCjmsO5o,10349
torchvision/datasets/clevr.py,sha256=Yw2dTlep-ERTzIsKHPGL9cblF88mGlRcoGoBGac1XZ0,3460
torchvision/datasets/coco.py,sha256=Zmfp6yZgWcDxXLDshcTnxDaKC6xvYsasPcBh_j9E9m4,4180
torchvision/datasets/country211.py,sha256=T_WIsox6Ve6CxmFwnx6bX3KkLy1xzBCbAFBcGqHVYC8,2436
torchvision/datasets/dtd.py,sha256=c6GtnNd4xj4BCE52GMaXnn-AnZm7yn9Yha8Iwb5xhCo,4019
torchvision/datasets/eurosat.py,sha256=nKBDlYaYupwughReDD7Z_EH_WVTqqSyGRBjnIjmvUUk,2307
torchvision/datasets/fakedata.py,sha256=gKmN6VyQzWjjeEPpLPxb9i4DWwW-MtGVJfZf8uwHgyo,2447
torchvision/datasets/fer2013.py,sha256=f_Zj3Qf32x8ew5dZu8A03uph3I4AUvmmZabaLhTSMnU,5118
torchvision/datasets/fgvc_aircraft.py,sha256=Y5P7SsYLeXDuxy7VHVTx9TYDKHloxtxlxT4JBDgbvXg,4626
torchvision/datasets/flickr.py,sha256=rcbyRlYd-d_vRW9qmOPfX1bKBgFu4NbF-qlldqt2mcU,5431
torchvision/datasets/flowers102.py,sha256=SdPXQtHAeZ5Iod0xyK2Xq7n0ENA6YIoEUFfRqiBu1Q0,4641
torchvision/datasets/folder.py,sha256=bh7Jv0BOphBkKYxD-BogUWexE9RIrGR0FLM5MR24aGM,12919
torchvision/datasets/food101.py,sha256=1vbbbahI-Lp9xySy5bsnS50TeV93ovesSIotY0astw0,3752
torchvision/datasets/gtsrb.py,sha256=0n6GQIGPuKU7yA0tSpiAA1UktoShE2vzeA2EqhQZK-Q,3785
torchvision/datasets/hmdb51.py,sha256=lC16QNHvbKkS8QfgVdhBvSwN2eLRFUBUNL021nkvgdc,5971
torchvision/datasets/imagenet.py,sha256=kllmhLsUPgm88rww0j-OaEa-iuzGgyu49q6gphpXLjA,8691
torchvision/datasets/imagenette.py,sha256=R7rW9UeDQYNhBA49FkzzP66689zwHm8hdvlv6P3_2b0,4267
torchvision/datasets/inaturalist.py,sha256=izBRYXCGeCIRuhGP9BnwQpiJGNHFSneXqihPEDQwuas,9877
torchvision/datasets/kinetics.py,sha256=q2NZZtS9AXiAh6HZazqAnqfWuFZepvDzfbwlhM2qhnw,9878
torchvision/datasets/kitti.py,sha256=8mCScWNce0OdG3b6vWCJGR370CydbK2Iy8W96Dfsl0I,5637
torchvision/datasets/lfw.py,sha256=C3bEBH7NmpqesMNqz1FGbq-KgT3mZwmr1aWBFqYBFpE,10501
torchvision/datasets/lsun.py,sha256=m79MxqgRs04hLyj18tk5ZL1OFO4-Sc9ipd4gMlsqlnQ,5743
torchvision/datasets/mnist.py,sha256=_XSFsJUxtSPzWb-hDb_A7CHRK1A285czOJTYpE2LP4s,21751
torchvision/datasets/moving_mnist.py,sha256=6yCTZVgIlWy2f9bNlrAjpUWryeLohaWuN0bRhMdAERw,3644
torchvision/datasets/omniglot.py,sha256=nu72y9QlMfufNKMxFaJMDz_vtP_0VZ3-1QrtR9xjfy0,4092
torchvision/datasets/oxford_iiit_pet.py,sha256=t4me06AwjDjSTIE7f80VFuGxISGHFPz6B4Sn3uOrCBw,5519
torchvision/datasets/pcam.py,sha256=Ub7UWrAufIzLXN8p6Cunt7osnHCNTL-sxDmEMGypq2Q,5285
torchvision/datasets/phototour.py,sha256=V5UToh6KcI2hA5BlHwKVNdCXxb52YLTjS4lhz2JW8jQ,7868
torchvision/datasets/places365.py,sha256=Qk92EEcwDrG1gBX3uVycakPZRQBb_ZOG-6tJz78qFXk,7070
torchvision/datasets/rendered_sst2.py,sha256=2NRiL3I1hDrOdNllubdQ-gQ-Unaaqb2mLAXG4_JL5wY,3597
torchvision/datasets/samplers/__init__.py,sha256=W1ZtQpGLG6aoHylo1t8PEsHIVoWwso5bSFk9JzKfH8g,161
torchvision/datasets/samplers/__pycache__/__init__.cpython-312.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-312.pyc,,
torchvision/datasets/samplers/clip_sampler.py,sha256=1-k3bxToGpBlqC4-iyVDggtojA701NflW0nBRLK27tQ,6244
torchvision/datasets/sbd.py,sha256=BpowMEO3_IxJgyjrtEN7XSLAKlrONVhCGr2kJXtTIzs,5414
torchvision/datasets/sbu.py,sha256=QV_1aKa9xffZR4JZmyWg3izqoytfnuJKsYjcF4Mjqns,4084
torchvision/datasets/semeion.py,sha256=-mIPORNUG5n7eNWq4VRLi9imlHm_bHsWKMv3E9HsAKo,3089
torchvision/datasets/stanford_cars.py,sha256=UuNAZe6j7Z6g04RZEoYASPtpABwOI1--hAwxtcBxv18,4898
torchvision/datasets/stl10.py,sha256=eQk04VL6mcKft57Yap7QATEXwerDWn5QrB-Lg7fVIX0,7234
torchvision/datasets/sun397.py,sha256=q_qfa_rdx4GUklR9oIHCgQC0JUKXMc7UudTq6yUeJPQ,2783
torchvision/datasets/svhn.py,sha256=Vk8VO74JUUaZHvejvkWJBRnmk-zpmHwjksMCZoBDWDc,4828
torchvision/datasets/ucf101.py,sha256=s7rHl7qonY7PnmEZac_O2gmJUIVFzyNxVbvMY7IY_Io,5533
torchvision/datasets/usps.py,sha256=7IP-xNZUJQNibubSodJgnpUJlCvNe-prd8BHsrbzSR0,3500
torchvision/datasets/utils.py,sha256=uN8Tt0S3dAuGI-wjyMzlX59QSQbUJkfUwRf_J20uX4I,15994
torchvision/datasets/video_utils.py,sha256=14GvzCRi7tbfeCq31MN9XP_6-bfewRSrvwavO4VBFdk,17213
torchvision/datasets/vision.py,sha256=x8AuTqEBwwBoHmkkWD6Iki8o5LMxac2yhrzIFBDgodE,4249
torchvision/datasets/voc.py,sha256=LhdQavn7-nq13zf9HIfjNYxPDa5SaTUDgayDe8uLfZc,8835
torchvision/datasets/widerface.py,sha256=HVdxQcpuPYPzjT07lWFAsMFKEu9bNkUSrC4ihTok56o,8264
torchvision/extension.py,sha256=YWBDURfCFXSmRvXi2iEg2L0hafN2-RnybpImh9JAUtQ,3141
torchvision/image.so,sha256=SuYQDTx8zpTMDX9sC2lZ6UeX-GZwIFhxBJgUf-RRTDo,667257
torchvision/io/__init__.py,sha256=-6ONNvXi97z0x3UjxLBwstOTG8KcJg_sFGha3oGFjAo,1583
torchvision/io/__pycache__/__init__.cpython-312.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-312.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-312.pyc,,
torchvision/io/__pycache__/image.cpython-312.pyc,,
torchvision/io/__pycache__/video.cpython-312.pyc,,
torchvision/io/__pycache__/video_reader.cpython-312.pyc,,
torchvision/io/_load_gpu_decoder.py,sha256=Cc8eP620qPDFc0q2qd-VYtjxtsgFPjOgg7Z04RXRziU,178
torchvision/io/_video_opt.py,sha256=oW2Vvs13fa9nopb4Ot3n_VNiOUCn5ZPLQnH8Xf8-81g,20456
torchvision/io/image.py,sha256=yXy4_g6_57LC-bEt3ukQbrpl6fsdeOaqDacGVJ6YtCc,21718
torchvision/io/video.py,sha256=uU8TqYeeQ1TpODZFj0qyQrSCLByIzOP5di_3Pr9estQ,18025
torchvision/io/video_reader.py,sha256=eI09x1vuUsbtL6rnyeiv894y8EA9bfdJakV1zWYzBtQ,11689
torchvision/models/__init__.py,sha256=A8GQPE1bl3oUHpuD9ND53DV557IPY4459FNLW6sVXGI,865
torchvision/models/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/__pycache__/_api.cpython-312.pyc,,
torchvision/models/__pycache__/_meta.cpython-312.pyc,,
torchvision/models/__pycache__/_utils.cpython-312.pyc,,
torchvision/models/__pycache__/alexnet.cpython-312.pyc,,
torchvision/models/__pycache__/convnext.cpython-312.pyc,,
torchvision/models/__pycache__/densenet.cpython-312.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-312.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-312.pyc,,
torchvision/models/__pycache__/googlenet.cpython-312.pyc,,
torchvision/models/__pycache__/inception.cpython-312.pyc,,
torchvision/models/__pycache__/maxvit.cpython-312.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-312.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-312.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-312.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-312.pyc,,
torchvision/models/__pycache__/regnet.cpython-312.pyc,,
torchvision/models/__pycache__/resnet.cpython-312.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-312.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-312.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-312.pyc,,
torchvision/models/__pycache__/vgg.cpython-312.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-312.pyc,,
torchvision/models/_api.py,sha256=uIIJnxX1zYMNpdvJ0haSq15_XlR1QteFZBYVAdtEheg,10054
torchvision/models/_meta.py,sha256=fqpeQBsf9EEYbmApQ8Q0LKyM9_UFwjireII5mwDbwJY,28875
torchvision/models/_utils.py,sha256=S8uDD7maNefy-fEW6mpz8dFU68acK1HxN0kt1qpkkDo,10893
torchvision/models/alexnet.py,sha256=dvBZLVH60TOTHCNNkWg0TFLtuJ5Ghh_xXN73r3Vyq58,4488
torchvision/models/convnext.py,sha256=tP73tH-us6h2KSdVcPypEX9Izk5lsr82KsGT15mj4NE,15326
torchvision/models/densenet.py,sha256=OZEsHJw76kOSRG4TKhLy7lPGsGEixy6llHkpC8snSOo,16825
torchvision/models/detection/__init__.py,sha256=JwYm_fTGO_FeRg4eTOQLwQPZ9lC9jheZ-QEoJgqKTjg,168
torchvision/models/detection/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-312.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-312.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-312.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-312.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-312.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-312.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-312.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-312.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-312.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-312.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-312.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-312.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-312.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-312.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-312.pyc,,
torchvision/models/detection/_utils.py,sha256=2y3FQ4F5yXhFM7VIWmu_70FpKgZjxdT_ucfzYwi3ZUQ,22127
torchvision/models/detection/anchor_utils.py,sha256=8Ix1Vp3i2kgJGr6esie3rw0_yAjtrUSvLXVKPaoZeQo,11859
torchvision/models/detection/backbone_utils.py,sha256=4FyzocR6YS7cG5IJTMRwC44tupbXQDA_Ru_8qqaju2I,10548
torchvision/models/detection/faster_rcnn.py,sha256=8DnegLKZnr8Q-zrzGT7_peIc_k_R1q1ijDH5n1P3gQE,36979
torchvision/models/detection/fcos.py,sha256=8r8MayvUMeTKfDoza4Hy67ChgRglLzBG6YS5qNe84sM,34235
torchvision/models/detection/generalized_rcnn.py,sha256=4-Dp8Vx-SjDDSZ7TsZ11rmkvEH336aLuSOlERXiQ7fs,4743
torchvision/models/detection/image_list.py,sha256=SUJ3xMn-1xc6ivYZUNIdWBh3RH9xD8EtCdpsXnPI_iM,783
torchvision/models/detection/keypoint_rcnn.py,sha256=4HxwRrp8lJfdyi8K3eBq4vstbRrL8bZc2Hhh-pVHjsI,21947
torchvision/models/detection/mask_rcnn.py,sha256=X1GQS314qOy4uCCp7MPfH6W12IydRwW-tDCmCnB1FGg,26713
torchvision/models/detection/retinanet.py,sha256=17Q0RdqqugASEVDGJfr8lCD61zjEqD5XxwQZAmZUZ24,37300
torchvision/models/detection/roi_heads.py,sha256=Uh9950xZUEmejwD2pRRhKvqNV0bY_G2Om8yGC2EdDDg,33822
torchvision/models/detection/rpn.py,sha256=7jbqPpLelnGCb5Fn-muUXeZF9EQ2nhE5r2aNAuR9V0M,15838
torchvision/models/detection/ssd.py,sha256=tbsgVbRD36WrjkZEB4xi1fvOXT62ry0p8G_Sd-j5CrY,28979
torchvision/models/detection/ssdlite.py,sha256=8nyEUYONUYe319JpgevKEfjr_FxCgDNU8gOyfuZ3L3c,13219
torchvision/models/detection/transform.py,sha256=Ma0CDvLCMlk3MxS3asXcDxrSosRLacaLpi-T34LXm1A,12189
torchvision/models/efficientnet.py,sha256=4qyeoXkYGFyUsBDt8TygDYycMMt1zhGwB_l4PmoPv4g,43090
torchvision/models/feature_extraction.py,sha256=8zZi6BF-3grrGAC61mgYzzvoYk_tSEZOWojBUK7FRBM,27933
torchvision/models/googlenet.py,sha256=b_TaeoABP08AVa8VqTreDdAuAnQTl54b6B1JOHbLy3A,12806
torchvision/models/inception.py,sha256=ifrLErzOVG-vlwQOMXLX5yMgcpHxCQQ17L7Wacn5QhQ,18851
torchvision/models/maxvit.py,sha256=AZkORRMQt7QRlPP-AiUxdwxGUEV6mR408KYbwUuxTSE,32096
torchvision/models/mnasnet.py,sha256=h9jY1TupaChZj9khnXya_l4O1exUWhWOOCmhJCCImKc,17574
torchvision/models/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/mobilenetv2.py,sha256=v9cRBAp7_C_50JFkjGZ0luvuh45oCYgYn37pcG2UL8o,9710
torchvision/models/mobilenetv3.py,sha256=-Xk32m_Wdn-ap8wCL4Tl7wjiROIwDwhasInYTMwwOrE,16279
torchvision/models/optical_flow/__init__.py,sha256=0zRlMWQJCjFqoUafUXVgO89-z7em7tACo9E8hHSq9RQ,20
torchvision/models/optical_flow/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-312.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-312.pyc,,
torchvision/models/optical_flow/_utils.py,sha256=v-tQJzYmYukrD1sQAE-5j5jxyvComwF1UdGkz5tVTLw,2077
torchvision/models/optical_flow/raft.py,sha256=FpSLPXisugu5Rzp_D5XCr037snBapMJ0dDPrw9c3CNk,39995
torchvision/models/quantization/__init__.py,sha256=gqFM7zI4UUHKKBDJAumozOn7xPL0JtvyNS8Ejz6QXp0,125
torchvision/models/quantization/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-312.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-312.pyc,,
torchvision/models/quantization/googlenet.py,sha256=C-8lm9TnjkEuwu6zaPp0r5mb0QMYvTMGOtz2--s1IFo,8080
torchvision/models/quantization/inception.py,sha256=hg8K1QNk7T-Qo3zOB47eupS3Thu_RjVI6mG2HzAEx8M,10815
torchvision/models/quantization/mobilenet.py,sha256=lSRVxw2TL3LFBwCadvyvH6n3GzqUTnK2-rhX3MOgSrs,211
torchvision/models/quantization/mobilenetv2.py,sha256=ggpNLU4_JkyMn8IPTgj1p0xx_Wvspcii2Wd3ISj5tBE,5883
torchvision/models/quantization/mobilenetv3.py,sha256=PVWmSP62Pn8hQkd682l6uYFLQp1nxZltMOE-FhhO9OU,9230
torchvision/models/quantization/resnet.py,sha256=9Hb6KyPv33Jj1A6JciXvGX06q0RkwwP10u8GxFfmorM,17939
torchvision/models/quantization/shufflenetv2.py,sha256=eS2y34ZTG03dNJgtVJ2qSXQWZ22PHIWBYeC8cbvI1yI,16884
torchvision/models/quantization/utils.py,sha256=n8mWsK9_Ek_M2AqGKPLoLlcKaYGH2PrF2l5_W84oBMk,2058
torchvision/models/regnet.py,sha256=-7s5n0qzXZPR9HgzOk9aj1sv9dWZ3AxnP7CmZRdUeZI,63553
torchvision/models/resnet.py,sha256=dJmlBZrXsaH491Q8BLShN5UUD62DfDhTC0j_XZYQv24,38932
torchvision/models/segmentation/__init__.py,sha256=TGk6UdVXAMtwBpYalrvdXZnmSwqzTDOT1lgKrfzhHrQ,66
torchvision/models/segmentation/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-312.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-312.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-312.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-312.pyc,,
torchvision/models/segmentation/_utils.py,sha256=QfyqCtH_MJnIkKW5m-98GZD2MjtPYLtPTDi79pcIGhs,1197
torchvision/models/segmentation/deeplabv3.py,sha256=wVgXz21sugSck2KbG7WD-wgMwCAW0wd8jBGhgue300s,15015
torchvision/models/segmentation/fcn.py,sha256=I1FqaZZVPc3Fbg_7E2L5qpumnupxBYc7KYsW03EG_Cs,8973
torchvision/models/segmentation/lraspp.py,sha256=dt5DJ_qbDZlEM0SIuN87JU43JHfVlb8Oepp76KDv5tw,7643
torchvision/models/shufflenetv2.py,sha256=84FiPfkhJpSw6Q9Jmaug5MW5qmWCO3VhAPF61EiMn7Q,15444
torchvision/models/squeezenet.py,sha256=apjFPEI5nr_493bAQsR245EorzaMYXVQSqdcveyAfy0,8763
torchvision/models/swin_transformer.py,sha256=VwvnImWcjblashj0OONycDJnIkz-zRDpm365v_a0-zo,39337
torchvision/models/vgg.py,sha256=jYjIoY2jtKAc-aURCQsvbgBxup1Gh4fVZSt2NzFLlZY,19225
torchvision/models/video/__init__.py,sha256=O4HB-RaXgCtnvpMDAuMBaIeKIiYEkNxra_fmAHLUIJM,93
torchvision/models/video/__pycache__/__init__.cpython-312.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-312.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-312.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-312.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-312.pyc,,
torchvision/models/video/mvit.py,sha256=0AZ31K5QcUBWZUUPTI1FCCM2Fma95bPs1o82zzpw2i0,32998
torchvision/models/video/resnet.py,sha256=RUnbUXFmoWNo_XbEKLmVSM8LUDcyv6jGZJ8GGpZi_6U,16771
torchvision/models/video/s3d.py,sha256=jx9gMP18Bzb7UO3vjejVBHlrCrJPdWFDfTn7XeU5kMg,7815
torchvision/models/video/swin_transformer.py,sha256=3GMyPGPeMcwJ1p9TGiRbpIlP-G7Qv_jWNbZmqIwMNyA,27688
torchvision/models/vision_transformer.py,sha256=O4mdBjYFsp-HTZA9bXfux_wJzIPRv1uS43PjuNh52zc,32136
torchvision/ops/__init__.py,sha256=eVv16QSBwgKaojOUHMPCy4ou9ZeFh-HoCV4DpqrZG4U,1928
torchvision/ops/__pycache__/__init__.cpython-312.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-312.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-312.pyc,,
torchvision/ops/__pycache__/_utils.cpython-312.pyc,,
torchvision/ops/__pycache__/boxes.cpython-312.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-312.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-312.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-312.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-312.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-312.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-312.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-312.pyc,,
torchvision/ops/__pycache__/misc.cpython-312.pyc,,
torchvision/ops/__pycache__/poolers.cpython-312.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-312.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-312.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-312.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-312.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-312.pyc,,
torchvision/ops/_box_convert.py,sha256=_bRRpErwk03rcPuscO1tCI9v3l88oNlDBDl2jzPlbKo,2409
torchvision/ops/_register_onnx_ops.py,sha256=Fyb1kC2m2OqZdfW_M86pt9-S66e1qNUhXNu1EQRa034,4181
torchvision/ops/_utils.py,sha256=pVHPpsmx6XcfGjUVk-XAEnd8QJBkrw_cT6fO_IwICE4,3630
torchvision/ops/boxes.py,sha256=ti47P4NwxY3OPRowTVBinsXFWVsqYPMrHOEsdJCwP5E,16605
torchvision/ops/ciou_loss.py,sha256=3HClrMMKOJ3bndIUinNp3cp6Cim4-ZmmfuLn1-NPDUo,2756
torchvision/ops/deform_conv.py,sha256=fJxkVR_p_OQMzMja4flvmTgqDPvrOOcwzDG8bV7Q7pE,6990
torchvision/ops/diou_loss.py,sha256=tssNJhII4WT-wmidFS8gFNteQIAJz-Nd1Q7Trz1BjIY,3362
torchvision/ops/drop_block.py,sha256=A4EGIl7txrU_QmkI1N0W9hfd8tq8yx6zq32oYXaddLQ,5855
torchvision/ops/feature_pyramid_network.py,sha256=mfkaygxRz-0TAdTMq2fCAL-E0WxlRnTfdb-s_J5qPE4,8702
torchvision/ops/focal_loss.py,sha256=9kFqGyA0-hodRw9Au74k-FuS14OhsAvbFxDGvpx08Sg,2261
torchvision/ops/giou_loss.py,sha256=OXSaMZDZ0qy7jgaQ9exB_DMQXzcATBAFiIjzSlOV-bQ,2696
torchvision/ops/misc.py,sha256=yFnK7GT9OCMfDrn4NtQXKdh5broi1xocL94SoyqhWuw,13572
torchvision/ops/poolers.py,sha256=zzYhH7poMwGlYxDvAvCaL9emg9X7sM4xZFLEy0zvv5s,11920
torchvision/ops/ps_roi_align.py,sha256=4iAbeUVTessAcxvJhuARN_aFGUTZC9R4KrKC_mBH3MQ,3625
torchvision/ops/ps_roi_pool.py,sha256=jOv-2pAZdLFvvt4r4NwiRfxU5WAOy_vi6gxZjMvlusw,2870
torchvision/ops/roi_align.py,sha256=Ig9jLul90wBM3kaZuYEutsJEXfaCo3D0s_PxYMr9jQc,11292
torchvision/ops/roi_pool.py,sha256=70ou6Xc7qJxKe3SC54QIW3L99PoS0gLlwGocaYDbD2w,2943
torchvision/ops/stochastic_depth.py,sha256=ISZ9noJyZLxpTG-wa2VmPs66qjhVsP7ZxWHvumWSP3U,2236
torchvision/transforms/__init__.py,sha256=EMft42B1JAiU11J1rxIN4Znis6EJPbp-bsGjAzH-24M,53
torchvision/transforms/__pycache__/__init__.cpython-312.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-312.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-312.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-312.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-312.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-312.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-312.pyc,,
torchvision/transforms/__pycache__/functional.cpython-312.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-312.pyc,,
torchvision/transforms/_functional_pil.py,sha256=LZO3ZBJRECXQJCDK9h_1TDYOFOajN_QSeASLQr1xMPg,12070
torchvision/transforms/_functional_tensor.py,sha256=3cEs8IYfRNQyff5Iriv--cZTWOIfvw2eaWiHU1-94AE,33939
torchvision/transforms/_functional_video.py,sha256=YcV557YglbJsq9SRGJHFoRbtxawiLSJ1oM5rV75OyqQ,3857
torchvision/transforms/_presets.py,sha256=RAjD6DgpU4QnNxV0MfZ3uHgzuARf-cdxD3Vo_KKIYeY,8510
torchvision/transforms/_transforms_video.py,sha256=Buz5LCWVPGiEonHE-cXIXfbkBhNc0qxVraxkNdxKp8o,4950
torchvision/transforms/autoaugment.py,sha256=JcbdEDbR0-OqTE4cwkhVSB45woFZQ_Fq5xmjFu_3bjg,28243
torchvision/transforms/functional.py,sha256=r9DojEr-0oqCOLuSMH0B4kWtI3UEbY_4jS7RBWDZKqM,67855
torchvision/transforms/transforms.py,sha256=eRIUr0I1i7BMqrXm4xsBJQYYGpvIkDr_VMsctQOI0M4,85547
torchvision/transforms/v2/__init__.py,sha256=UUJgzZguNPl7B33Zt3gexO7gSApSuWHTpzE7fNXQpA0,1545
torchvision/transforms/v2/__pycache__/__init__.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-312.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-312.pyc,,
torchvision/transforms/v2/_augment.py,sha256=YnxyCNZvYYredkImS-IasQ_HkBakHs0y2cXkdfSg2ek,16154
torchvision/transforms/v2/_auto_augment.py,sha256=sQWkEF1N17XU4F6nBGva7kUuiuGNEOCAGHYGn8oa0A8,32025
torchvision/transforms/v2/_color.py,sha256=cYulJPZkwv6FpBBxwSe4Lq3qXltf6vos5urxvBR4OL4,16978
torchvision/transforms/v2/_container.py,sha256=SFh-FU8ceir934hxS_VkbVQq0SxzGSULPaYpouJJhPs,6055
torchvision/transforms/v2/_deprecated.py,sha256=X_80zQwRKzGEcs8yq9JqhKBK1W56J121kyaQv_PVkQM,1946
torchvision/transforms/v2/_geometry.py,sha256=xjd2_TavgN8Dm_xphLmRcIvwnpcu1KLz3nPzZpUoQYM,67585
torchvision/transforms/v2/_meta.py,sha256=mTunFNfyvhG3arMGnUJH6Cy5MRlrHOIfkv4SxlAhWaI,1403
torchvision/transforms/v2/_misc.py,sha256=e3M60ihEhkx4Riuj5DOIhWhWNo-QLSI-pmreJhJiSRM,19102
torchvision/transforms/v2/_temporal.py,sha256=1sd0pJuou0l9gNHjHFA40dSYRB-JkDvvFOia7WcIQ50,905
torchvision/transforms/v2/_transform.py,sha256=QP35AB088j2Z6ZUwGAI6S5mM2FsXtX_5nKuKp1orA2s,9367
torchvision/transforms/v2/_type_conversion.py,sha256=DJLueqE-07JADokRTY3fD9_qktdRpFDzGn9dxzXCT1A,2856
torchvision/transforms/v2/_utils.py,sha256=zOOoaiOfivVfIVTo5GLSAAlknhcr6G7A9Tnkclrl0OQ,8846
torchvision/transforms/v2/functional/__init__.py,sha256=4SDjzgj9e4oM4IUKy9YJAwXFnBoLpygd8sSM_7sMvK0,3546
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-312.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-312.pyc,,
torchvision/transforms/v2/functional/_augment.py,sha256=MRM8E3_gKfTTC0qFt3cKI4UxTxQtuGI9MeY2mBsrj04,3473
torchvision/transforms/v2/functional/_color.py,sha256=nUASg1bTHmsf2AT_1Q7CLNXhObrRPbB1w2fDuz9k5e8,30244
torchvision/transforms/v2/functional/_deprecated.py,sha256=ycYZLDwDyd612aPbTKIV3gqhCRLMdF03MQELct4LeGs,801
torchvision/transforms/v2/functional/_geometry.py,sha256=5QL4IdQV72PkJX61c4A5M4WLq60ihTQB6g1PE9tMqmM,87520
torchvision/transforms/v2/functional/_meta.py,sha256=AxTEF6mdybAW1lC_DcjfKlxvSuiVupnqbJJrqS5x4lc,10547
torchvision/transforms/v2/functional/_misc.py,sha256=OXu4GTCF9i_1lz7T62gKcEs94faBO7wyYmpUOCnkUEY,17517
torchvision/transforms/v2/functional/_temporal.py,sha256=24CQCXXO12TnW7aUiUQdrk5DRSpTPONjjC4jaGh3lH4,1136
torchvision/transforms/v2/functional/_type_conversion.py,sha256=78wl0dNPwX08jOCW6KcZSGy8RAQqyxMtdrTUQVQlUTM,869
torchvision/transforms/v2/functional/_utils.py,sha256=tsmwIF37Z9QnP9x3x4hAs1hLrcvL78GLkuO6Rq1EUTk,5479
torchvision/tv_tensors/__init__.py,sha256=C6N8p5aulpehsOBBmH1cPIY1xiOSASZVBfnlXgGvR_s,1509
torchvision/tv_tensors/__pycache__/__init__.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-312.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-312.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,sha256=_-bDwN1gnHpfnHXEK0O6bQrcEOv656VOliHOgoNstpw,4493
torchvision/tv_tensors/_dataset_wrapper.py,sha256=fNnk3CSXipBNFsmnsPpa10DRN0I_Ly4Xib2Y5Zng9Ro,24505
torchvision/tv_tensors/_image.py,sha256=bwx4n8qObrknE3xEIDJOs0vWJzCg4XISjtXR7ksJTgs,1934
torchvision/tv_tensors/_mask.py,sha256=s85DdYFK6cyrL0_MnhAC2jTJxZzL7MJ8DTx985JPVhQ,1478
torchvision/tv_tensors/_torch_function_helpers.py,sha256=81qDZqgzUeSgfSeWhsrw1Ukwltvf97WbwmKWHm7X8X0,2276
torchvision/tv_tensors/_tv_tensor.py,sha256=dGQJhvOVTjb1LVT5qPZLJxox30uDMmODB26Iz6TjVbc,6248
torchvision/tv_tensors/_video.py,sha256=4dQ5Rh_0ghPtaLVSOxVWXJv1uWi8ZKXlfbRsBZ3roxw,1416
torchvision/utils.py,sha256=Bu04d39mBjf2Q_eMb_uZVtLL5tGl3HhC18VvB_lXgQM,27306
torchvision/version.py,sha256=ZU-VOJy0kSuBVeSZSUN2GnuMwkjzmN-nOOa4Y54S-0M,203
